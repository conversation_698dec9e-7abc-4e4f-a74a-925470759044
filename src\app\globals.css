@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont,
    "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif,
    "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
  --font-mono: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas,
    "Liberation Mono", "Courier New", monospace;
  --color-sidebar-ring: var(--sidebar-ring);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar: var(--sidebar);
  --color-chart-5: var(--chart-5);
  --color-chart-4: var(--chart-4);
  --color-chart-3: var(--chart-3);
  --color-chart-2: var(--chart-2);
  --color-chart-1: var(--chart-1);
  --color-ring: var(--ring);
  --color-input: var(--input);
  --color-border: var(--border);
  --color-destructive: var(--destructive);
  --color-accent-foreground: var(--accent-foreground);
  --color-accent: var(--accent);
  --color-muted-foreground: var(--muted-foreground);
  --color-muted: var(--muted);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-secondary: var(--secondary);
  --color-primary-foreground: var(--primary-foreground);
  --color-primary: var(--primary);
  --color-popover-foreground: var(--popover-foreground);
  --color-popover: var(--popover);
  --color-card-foreground: var(--card-foreground);
  --color-card: var(--card);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
  --font-serif: ui-serif, Georgia, Cambria, "Times New Roman", Times, serif;
  --radius: 0.65rem;
  --tracking-tighter: calc(var(--tracking-normal) - 0.05em);
  --tracking-tight: calc(var(--tracking-normal) - 0.025em);
  --tracking-wide: calc(var(--tracking-normal) + 0.025em);
  --tracking-wider: calc(var(--tracking-normal) + 0.05em);
  --tracking-widest: calc(var(--tracking-normal) + 0.1em);
  --tracking-normal: var(--tracking-normal);
  --shadow-2xl: var(--shadow-2xl);
  --shadow-xl: var(--shadow-xl);
  --shadow-lg: var(--shadow-lg);
  --shadow-md: var(--shadow-md);
  --shadow: var(--shadow);
  --shadow-sm: var(--shadow-sm);
  --shadow-xs: var(--shadow-xs);
  --shadow-2xs: var(--shadow-2xs);
  --spacing: var(--spacing);
  --letter-spacing: var(--letter-spacing);
  --shadow-offset-y: var(--shadow-offset-y);
  --shadow-offset-x: var(--shadow-offset-x);
  --shadow-spread: var(--shadow-spread);
  --shadow-blur: var(--shadow-blur);
  --shadow-opacity: var(--shadow-opacity);
  --color-shadow-color: var(--shadow-color);
  --color-destructive-foreground: var(--destructive-foreground);
}

:root {
  --radius: 0.65rem;
  --background: oklch(1 0 0);
  --foreground: oklch(0.145 0 0);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.145 0 0);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.145 0 0);
  --primary: oklch(0.205 0 0);
  --primary-foreground: oklch(0.985 0 0);
  --secondary: oklch(0.97 0 0);
  --secondary-foreground: oklch(0.205 0 0);
  --muted: oklch(0.97 0 0);
  --muted-foreground: oklch(0.556 0 0);
  --accent: oklch(0.97 0 0);
  --accent-foreground: oklch(0.205 0 0);
  --destructive: oklch(0.577 0.245 27.325);
  --border: oklch(0.922 0 0);
  --input: oklch(0.922 0 0);
  --ring: oklch(0.708 0 0);
  --chart-1: oklch(0.646 0.222 41.116);
  --chart-2: oklch(0.6 0.118 184.704);
  --chart-3: oklch(0.398 0.07 227.392);
  --chart-4: oklch(0.828 0.189 84.429);
  --chart-5: oklch(0.769 0.188 70.08);
  --sidebar: oklch(0.985 0 0);
  --sidebar-foreground: oklch(0.145 0 0);
  --sidebar-primary: oklch(0.205 0 0);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.97 0 0);
  --sidebar-accent-foreground: oklch(0.205 0 0);
  --sidebar-border: oklch(0.922 0 0);
  --sidebar-ring: oklch(0.708 0 0);
  --destructive-foreground: oklch(1 0 0);
  --font-sans: ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont,
    "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif,
    "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
  --font-serif: ui-serif, Georgia, Cambria, "Times New Roman", Times, serif;
  --font-mono: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas,
    "Liberation Mono", "Courier New", monospace;
  --shadow-color: oklch(0 0 0);
  --shadow-opacity: 0.1;
  --shadow-blur: 3px;
  --shadow-spread: 0px;
  --shadow-offset-x: 0;
  --shadow-offset-y: 1px;
  --letter-spacing: 0em;
  --spacing: 0.25rem;
  --shadow-2xs: 0 1px 3px 0px hsl(0 0% 0% / 0.05);
  --shadow-xs: 0 1px 3px 0px hsl(0 0% 0% / 0.05);
  --shadow-sm: 0 1px 3px 0px hsl(0 0% 0% / 0.1),
    0 1px 2px -1px hsl(0 0% 0% / 0.1);
  --shadow: 0 1px 3px 0px hsl(0 0% 0% / 0.1), 0 1px 2px -1px hsl(0 0% 0% / 0.1);
  --shadow-md: 0 1px 3px 0px hsl(0 0% 0% / 0.1),
    0 2px 4px -1px hsl(0 0% 0% / 0.1);
  --shadow-lg: 0 1px 3px 0px hsl(0 0% 0% / 0.1),
    0 4px 6px -1px hsl(0 0% 0% / 0.1);
  --shadow-xl: 0 1px 3px 0px hsl(0 0% 0% / 0.1),
    0 8px 10px -1px hsl(0 0% 0% / 0.1);
  --shadow-2xl: 0 1px 3px 0px hsl(0 0% 0% / 0.25);
  --tracking-normal: 0em;
}

.dark {
  --background: oklch(0.145 0 0);
  --foreground: oklch(0.985 0 0);
  --card: oklch(0.205 0 0);
  --card-foreground: oklch(0.985 0 0);
  --popover: oklch(0.205 0 0);
  --popover-foreground: oklch(0.985 0 0);
  --primary: oklch(0.922 0 0);
  --primary-foreground: oklch(0.205 0 0);
  --secondary: oklch(0.269 0 0);
  --secondary-foreground: oklch(0.985 0 0);
  --muted: oklch(0.269 0 0);
  --muted-foreground: oklch(0.708 0 0);
  --accent: oklch(0.269 0 0);
  --accent-foreground: oklch(0.985 0 0);
  --destructive: oklch(0.704 0.191 22.216);
  --border: oklch(1 0 0 / 10%);
  --input: oklch(1 0 0 / 15%);
  --ring: oklch(0.556 0 0);
  --chart-1: oklch(0.488 0.243 264.376);
  --chart-2: oklch(0.696 0.17 162.48);
  --chart-3: oklch(0.769 0.188 70.08);
  --chart-4: oklch(0.627 0.265 303.9);
  --chart-5: oklch(0.645 0.246 16.439);
  --sidebar: oklch(0.205 0 0);
  --sidebar-foreground: oklch(0.985 0 0);
  --sidebar-primary: oklch(0.488 0.243 264.376);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.269 0 0);
  --sidebar-accent-foreground: oklch(0.985 0 0);
  --sidebar-border: oklch(1 0 0 / 10%);
  --sidebar-ring: oklch(0.556 0 0);
  --destructive-foreground: oklch(1 0 0);
  --radius: 0.65rem;
  --font-sans: ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont,
    "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif,
    "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
  --font-serif: ui-serif, Georgia, Cambria, "Times New Roman", Times, serif;
  --font-mono: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas,
    "Liberation Mono", "Courier New", monospace;
  --shadow-color: oklch(0 0 0);
  --shadow-opacity: 0.1;
  --shadow-blur: 3px;
  --shadow-spread: 0px;
  --shadow-offset-x: 0;
  --shadow-offset-y: 1px;
  --letter-spacing: 0em;
  --spacing: 0.25rem;
  --shadow-2xs: 0 1px 3px 0px hsl(0 0% 0% / 0.05);
  --shadow-xs: 0 1px 3px 0px hsl(0 0% 0% / 0.05);
  --shadow-sm: 0 1px 3px 0px hsl(0 0% 0% / 0.1),
    0 1px 2px -1px hsl(0 0% 0% / 0.1);
  --shadow: 0 1px 3px 0px hsl(0 0% 0% / 0.1), 0 1px 2px -1px hsl(0 0% 0% / 0.1);
  --shadow-md: 0 1px 3px 0px hsl(0 0% 0% / 0.1),
    0 2px 4px -1px hsl(0 0% 0% / 0.1);
  --shadow-lg: 0 1px 3px 0px hsl(0 0% 0% / 0.1),
    0 4px 6px -1px hsl(0 0% 0% / 0.1);
  --shadow-xl: 0 1px 3px 0px hsl(0 0% 0% / 0.1),
    0 8px 10px -1px hsl(0 0% 0% / 0.1);
  --shadow-2xl: 0 1px 3px 0px hsl(0 0% 0% / 0.25);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
    letter-spacing: var(--tracking-normal);
  }
}

@layer utilities {
  /* Hide scrollbar for Chrome, Safari and Opera */
  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }

  /* Hide scrollbar for IE, Edge and Firefox */
  .scrollbar-hide {
    -ms-overflow-style: none; /* IE and Edge */
    scrollbar-width: none; /* Firefox */
  }
}

/* Advanced Tiptap Editor - Template Style */
.advanced-tiptap-editor .ProseMirror {
  outline: none;
  padding: 32px;
  min-height: 600px;
  font-size: 16px;
  line-height: 1.7;
  color: hsl(var(--foreground));
  background: hsl(var(--background));
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
    "Helvetica Neue", Arial, sans-serif;
  max-width: none;
  user-select: text;
  -webkit-user-select: text;
  -moz-user-select: text;
  -ms-user-select: text;
  cursor: text;
  caret-color: hsl(var(--foreground));
}

.advanced-tiptap-editor .ProseMirror p.is-editor-empty:first-child::before {
  content: attr(data-placeholder);
  float: left;
  color: hsl(var(--muted-foreground));
  pointer-events: none;
  height: 0;
  font-size: 16px;
}

/* Headings */
.tiptap-editor .ProseMirror h1 {
  font-size: 2rem;
  font-weight: 700;
  line-height: 1.2;
  margin: 1.5rem 0 1rem 0;
  color: hsl(var(--foreground));
}

.tiptap-editor .ProseMirror h1:first-child {
  margin-top: 0;
}

.tiptap-editor .ProseMirror h2 {
  font-size: 1.5rem;
  font-weight: 600;
  line-height: 1.3;
  margin: 1.25rem 0 0.75rem 0;
  color: hsl(var(--foreground));
}

.tiptap-editor .ProseMirror h3 {
  font-size: 1.25rem;
  font-weight: 600;
  line-height: 1.4;
  margin: 1rem 0 0.5rem 0;
  color: hsl(var(--foreground));
}

/* Paragraphs */
.tiptap-editor .ProseMirror p {
  margin: 0.75rem 0;
  line-height: 1.6;
}

.tiptap-editor .ProseMirror p:first-child {
  margin-top: 0;
}

.tiptap-editor .ProseMirror p:last-child {
  margin-bottom: 0;
}

/* Lists */
.tiptap-editor .ProseMirror ul,
.tiptap-editor .ProseMirror ol {
  margin: 0.75rem 0;
  padding-left: 1.5rem;
}

.tiptap-editor .ProseMirror ul li,
.tiptap-editor .ProseMirror ol li {
  margin: 0.25rem 0;
  line-height: 1.6;
}

.tiptap-editor .ProseMirror ul {
  list-style-type: disc;
}

.tiptap-editor .ProseMirror ol {
  list-style-type: decimal;
}

/* Blockquotes */
.tiptap-editor .ProseMirror blockquote {
  border-left: 4px solid hsl(var(--border));
  padding-left: 1rem;
  margin: 1rem 0;
  font-style: italic;
  color: hsl(var(--muted-foreground));
}

/* Code */
.tiptap-editor .ProseMirror code {
  background: hsl(var(--muted));
  color: hsl(var(--foreground));
  padding: 0.125rem 0.25rem;
  border-radius: 0.25rem;
  font-family: "SF Mono", Monaco, "Cascadia Code", "Roboto Mono", Consolas,
    "Courier New", monospace;
  font-size: 0.875em;
}

.tiptap-editor .ProseMirror pre {
  background: hsl(var(--muted));
  color: hsl(var(--foreground));
  padding: 1rem;
  border-radius: 0.5rem;
  margin: 1rem 0;
  overflow-x: auto;
  font-family: "SF Mono", Monaco, "Cascadia Code", "Roboto Mono", Consolas,
    "Courier New", monospace;
}

.tiptap-editor .ProseMirror pre code {
  background: none;
  padding: 0;
  font-size: inherit;
}

/* Links */
.tiptap-editor .ProseMirror a {
  color: #3b82f6;
  text-decoration: underline;
  text-underline-offset: 2px;
  cursor: pointer;
}

.tiptap-editor .ProseMirror a:hover {
  color: #2563eb;
}

/* Text formatting */
.tiptap-editor .ProseMirror strong {
  font-weight: 700;
}

.tiptap-editor .ProseMirror em {
  font-style: italic;
}

.tiptap-editor .ProseMirror s {
  text-decoration: line-through;
}

/* Text alignment */
.tiptap-editor .ProseMirror [data-text-align="left"] {
  text-align: left;
}

.tiptap-editor .ProseMirror [data-text-align="center"] {
  text-align: center;
}

.tiptap-editor .ProseMirror [data-text-align="right"] {
  text-align: right;
}

.tiptap-editor .ProseMirror [data-text-align="justify"] {
  text-align: justify;
}

/* Focus styles */
.tiptap-editor .ProseMirror:focus {
  outline: none;
}

/* Cursor visibility */
.tiptap-editor .ProseMirror {
  caret-color: hsl(var(--foreground));
}

.tiptap-editor .ProseMirror:focus {
  caret-color: hsl(var(--foreground));
}

/* Ensure cursor is visible in all text elements */
.tiptap-editor .ProseMirror h1,
.tiptap-editor .ProseMirror h2,
.tiptap-editor .ProseMirror h3,
.tiptap-editor .ProseMirror p,
.tiptap-editor .ProseMirror li,
.tiptap-editor .ProseMirror blockquote {
  caret-color: hsl(var(--foreground));
}

/* Dark mode cursor */
@media (prefers-color-scheme: dark) {
  .tiptap-editor .ProseMirror {
    caret-color: hsl(var(--foreground));
  }

  .tiptap-editor .ProseMirror:focus {
    caret-color: hsl(var(--foreground));
  }

  .tiptap-editor .ProseMirror h1,
  .tiptap-editor .ProseMirror h2,
  .tiptap-editor .ProseMirror h3,
  .tiptap-editor .ProseMirror p,
  .tiptap-editor .ProseMirror li,
  .tiptap-editor .ProseMirror blockquote {
    caret-color: hsl(var(--foreground));
  }
}

/* Selection styles - Better visibility */
.tiptap-editor .ProseMirror ::selection {
  background: hsl(var(--primary));
  color: hsl(var(--primary-foreground));
}

.tiptap-editor .ProseMirror::-moz-selection {
  background: hsl(var(--primary));
  color: hsl(var(--primary-foreground));
}

/* Ensure selection works on all text elements */
.tiptap-editor .ProseMirror h1::selection,
.tiptap-editor .ProseMirror h2::selection,
.tiptap-editor .ProseMirror h3::selection,
.tiptap-editor .ProseMirror p::selection,
.tiptap-editor .ProseMirror li::selection,
.tiptap-editor .ProseMirror blockquote::selection,
.tiptap-editor .ProseMirror code::selection,
.tiptap-editor .ProseMirror strong::selection,
.tiptap-editor .ProseMirror em::selection {
  background: hsl(var(--primary));
  color: hsl(var(--primary-foreground));
}

/* Dark mode selection */
@media (prefers-color-scheme: dark) {
  .tiptap-editor .ProseMirror ::selection {
    background: hsl(var(--primary));
    color: hsl(var(--primary-foreground));
  }

  .tiptap-editor .ProseMirror::-moz-selection {
    background: hsl(var(--primary));
    color: hsl(var(--primary-foreground));
  }

  .tiptap-editor .ProseMirror h1::selection,
  .tiptap-editor .ProseMirror h2::selection,
  .tiptap-editor .ProseMirror h3::selection,
  .tiptap-editor .ProseMirror p::selection,
  .tiptap-editor .ProseMirror li::selection,
  .tiptap-editor .ProseMirror blockquote::selection,
  .tiptap-editor .ProseMirror code::selection,
  .tiptap-editor .ProseMirror strong::selection,
  .tiptap-editor .ProseMirror em::selection {
    background: hsl(var(--primary));
    color: hsl(var(--primary-foreground));
  }
}

.tiptap-editor .ProseMirror * {
  user-select: text;
  -webkit-user-select: text;
  -moz-user-select: text;
  -ms-user-select: text;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .tiptap-editor .ProseMirror {
    padding: 16px;
    font-size: 15px;
  }

  .tiptap-editor .ProseMirror h1 {
    font-size: 1.75rem;
  }

  .tiptap-editor .ProseMirror h2 {
    font-size: 1.375rem;
  }

  .tiptap-editor .ProseMirror h3 {
    font-size: 1.125rem;
  }
}

/* Advanced Tiptap Editor Styles */
.advanced-tiptap-editor .ProseMirror {
  outline: none;
  padding: 32px;
  min-height: 500px;
  font-size: 16px;
  line-height: 1.7;
  color: hsl(var(--foreground));
  background: hsl(var(--background));
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
    "Helvetica Neue", Arial, sans-serif;
  max-width: none;
  user-select: text;
  -webkit-user-select: text;
  -moz-user-select: text;
  -ms-user-select: text;
  cursor: text;
  caret-color: hsl(var(--foreground));
}

/* Advanced Editor Focus */
.advanced-tiptap-editor .ProseMirror:focus {
  outline: none;
  caret-color: hsl(var(--foreground));
}

/* Advanced Editor Selection */
.advanced-tiptap-editor .ProseMirror ::selection {
  background: #3b82f6;
  color: white;
}

.advanced-tiptap-editor .ProseMirror::-moz-selection {
  background: #3b82f6;
  color: white;
}

/* Advanced Editor Typography */
.advanced-tiptap-editor .ProseMirror h1 {
  font-size: 2.25rem;
  font-weight: 700;
  line-height: 1.2;
  margin-top: 2rem;
  margin-bottom: 1rem;
}

.advanced-tiptap-editor .ProseMirror h2 {
  font-size: 1.875rem;
  font-weight: 600;
  line-height: 1.3;
  margin-top: 1.5rem;
  margin-bottom: 0.75rem;
}

.advanced-tiptap-editor .ProseMirror h3 {
  font-size: 1.5rem;
  font-weight: 600;
  line-height: 1.4;
  margin-top: 1.25rem;
  margin-bottom: 0.5rem;
}

.advanced-tiptap-editor .ProseMirror p {
  margin-bottom: 1rem;
}

.advanced-tiptap-editor .ProseMirror ul,
.advanced-tiptap-editor .ProseMirror ol {
  margin-bottom: 1rem;
  padding-left: 1.5rem;
}

.advanced-tiptap-editor .ProseMirror li {
  margin-bottom: 0.25rem;
}

.advanced-tiptap-editor .ProseMirror blockquote {
  border-left: 4px solid hsl(var(--border));
  padding-left: 1rem;
  margin: 1rem 0;
  font-style: italic;
  color: hsl(var(--muted-foreground));
}

.advanced-tiptap-editor .ProseMirror code {
  background: hsl(var(--muted));
  padding: 0.125rem 0.25rem;
  border-radius: 0.25rem;
  font-family: "Fira Code", "Monaco", "Cascadia Code", "Roboto Mono", monospace;
  font-size: 0.875rem;
}

.advanced-tiptap-editor .ProseMirror pre {
  background: hsl(var(--muted));
  padding: 1rem;
  border-radius: 0.5rem;
  overflow-x: auto;
  margin: 1rem 0;
}

.advanced-tiptap-editor .ProseMirror pre code {
  background: none;
  padding: 0;
  border-radius: 0;
}

/* Task Lists */
.advanced-tiptap-editor .ProseMirror ul[data-type="taskList"] {
  list-style: none;
  padding-left: 0;
}

.advanced-tiptap-editor .ProseMirror ul[data-type="taskList"] li {
  display: flex;
  align-items: flex-start;
  margin-bottom: 0.5rem;
}

.advanced-tiptap-editor .ProseMirror ul[data-type="taskList"] li > label {
  flex: 0 0 auto;
  margin-right: 0.5rem;
  user-select: none;
}

.advanced-tiptap-editor .ProseMirror ul[data-type="taskList"] li > div {
  flex: 1 1 auto;
}

/* Tables */
.advanced-tiptap-editor .ProseMirror table {
  border-collapse: collapse;
  table-layout: fixed;
  width: 100%;
  margin: 1rem 0;
  overflow: hidden;
}

.advanced-tiptap-editor .ProseMirror table td,
.advanced-tiptap-editor .ProseMirror table th {
  min-width: 1em;
  border: 1px solid hsl(var(--border));
  padding: 0.5rem;
  vertical-align: top;
  box-sizing: border-box;
  position: relative;
}

.advanced-tiptap-editor .ProseMirror table th {
  font-weight: 600;
  background: hsl(var(--muted));
}

.advanced-tiptap-editor .ProseMirror table .selectedCell:after {
  z-index: 2;
  position: absolute;
  content: "";
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  background: rgba(59, 130, 246, 0.1);
  pointer-events: none;
}

/* Horizontal Rule */
.advanced-tiptap-editor .ProseMirror hr {
  border: none;
  border-top: 2px solid hsl(var(--border));
  margin: 2rem 0;
}

/* Highlight */
.advanced-tiptap-editor .ProseMirror mark {
  background: hsl(var(--accent));
  color: hsl(var(--accent-foreground));
  padding: 0.125rem 0.25rem;
  border-radius: 0.25rem;
}

/* Links */
.advanced-tiptap-editor .ProseMirror a {
  color: hsl(var(--primary));
  text-decoration: underline;
  cursor: pointer;
}

.advanced-tiptap-editor .ProseMirror a:hover {
  color: hsl(var(--primary) / 0.8);
}

/* Images */
.advanced-tiptap-editor .ProseMirror img {
  max-width: 100%;
  height: auto;
  border-radius: 0.5rem;
  margin: 1rem 0;
}

/* Focus styling */
.advanced-tiptap-editor .ProseMirror.has-focus {
  outline: none;
}

/* Placeholder */
.advanced-tiptap-editor .ProseMirror p.is-editor-empty:first-child::before {
  content: attr(data-placeholder);
  float: left;
  color: hsl(var(--muted-foreground));
  pointer-events: none;
  height: 0;
}

/* Dark mode adjustments */
@media (prefers-color-scheme: dark) {
  .advanced-tiptap-editor .ProseMirror mark {
    background: hsl(var(--accent));
    color: hsl(var(--accent-foreground));
  }

  .advanced-tiptap-editor .ProseMirror ::selection {
    background: hsl(var(--primary));
    color: hsl(var(--primary-foreground));
  }

  .advanced-tiptap-editor .ProseMirror::-moz-selection {
    background: hsl(var(--primary));
    color: hsl(var(--primary-foreground));
  }
}

/* Hide scrollbars for toolbar */
.scrollbar-hide {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

.scrollbar-hide::-webkit-scrollbar {
  display: none;
}

/* Dashboard layout fixes */
.dashboard-layout {
  height: 100vh;
  overflow: hidden;
}

/* Tiptap editor layout - prevent page scrolling */
.tiptap-editor-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

/* Ensure toolbar stays fixed in position */
.tiptap-toolbar-fixed {
  flex-shrink: 0;
  background: hsl(var(--background));
  border-bottom: 1px solid hsl(var(--border));
  z-index: 40;
}

/* Content area should be the only scrollable part */
.tiptap-content-scrollable {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
}

/* Smooth scrolling for content */
.tiptap-content-scrollable {
  scroll-behavior: smooth;
}

/* Ensure proper spacing and layout */
.tiptap-title-container {
  flex-shrink: 0;
  background: hsl(var(--background));
}

/* Remove any unwanted margins or padding that might cause spacing issues */
.tiptap-minimal-spacing {
  margin: 0;
  padding: 0;
}

/* Simple Tiptap Editor Styles - Matching template.tiptap.dev/simple */
.ProseMirror {
  outline: none;
  font-family: ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont,
    "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif;
  font-size: 16px;
  line-height: 1.6;
  color: #374151;
  padding: 2rem;
  min-height: 500px;
}

.ProseMirror h1 {
  font-size: 2.25rem;
  font-weight: 700;
  line-height: 1.2;
  margin-top: 2rem;
  margin-bottom: 1rem;
  color: #111827;
}

.ProseMirror h2 {
  font-size: 1.875rem;
  font-weight: 600;
  line-height: 1.3;
  margin-top: 1.5rem;
  margin-bottom: 0.75rem;
  color: #111827;
}

.ProseMirror h3 {
  font-size: 1.5rem;
  font-weight: 600;
  line-height: 1.4;
  margin-top: 1.25rem;
  margin-bottom: 0.5rem;
  color: #111827;
}

.ProseMirror p {
  margin-bottom: 1rem;
}

.ProseMirror ul,
.ProseMirror ol {
  margin: 1rem 0;
  padding-left: 1.5rem;
}

.ProseMirror li {
  margin-bottom: 0.25rem;
}

.ProseMirror blockquote {
  border-left: 4px solid #e5e7eb;
  padding-left: 1rem;
  margin: 1.5rem 0;
  font-style: italic;
  color: #6b7280;
}

.ProseMirror code {
  background-color: #f3f4f6;
  padding: 0.125rem 0.25rem;
  border-radius: 0.25rem;
  font-family: ui-monospace, SFMono-Regular, "SF Mono", Consolas,
    "Liberation Mono", Menlo, monospace;
  font-size: 0.875em;
  color: #dc2626;
}

.ProseMirror pre {
  background-color: #f9fafb;
  border: 1px solid #e5e7eb;
  border-radius: 0.5rem;
  padding: 1rem;
  margin: 1.5rem 0;
  overflow-x: auto;
}

.ProseMirror pre code {
  background: none;
  padding: 0;
  color: #374151;
}

.ProseMirror a {
  color: hsl(var(--primary));
  text-decoration: underline;
  cursor: pointer;
}

.ProseMirror a:hover {
  color: hsl(var(--primary) / 0.8);
}

/* Placeholder styling */
.ProseMirror p.is-editor-empty:first-child::before {
  content: attr(data-placeholder);
  float: left;
  color: hsl(var(--muted-foreground));
  pointer-events: none;
  height: 0;
}

/* Selection styling */
.ProseMirror ::selection {
  background-color: hsl(var(--primary) / 0.2);
}

/* Focus styling */
.ProseMirror:focus {
  outline: none;
}
