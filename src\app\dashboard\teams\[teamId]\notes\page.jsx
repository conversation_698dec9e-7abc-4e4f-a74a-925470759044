"use client";

import React, { useState, useEffect } from "react";
import { useParams, useRouter } from "next/navigation";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import {
  FileText,
  Plus,
  Search,
  MoreVertical,
  FolderPlus,
  ArrowLeft,
  Calendar,
  User,
  Folder,
} from "lucide-react";
import { toast } from "sonner";
import Link from "next/link";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

export default function AllTeamNotesPage() {
  const params = useParams();
  const router = useRouter();
  const teamId = params.teamId;

  const [teamNotes, setTeamNotes] = useState([]);
  const [teamFolders, setTeamFolders] = useState([]);
  const [team, setTeam] = useState(null);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState("");

  // Fetch team data
  useEffect(() => {
    const fetchTeamData = async () => {
      try {
        const [teamRes, notesRes, foldersRes] = await Promise.all([
          fetch(`/api/teams/${teamId}`),
          fetch(`/api/teams/${teamId}/notes`),
          fetch(`/api/teams/${teamId}/folders`),
        ]);

        if (teamRes.ok) {
          const teamData = await teamRes.json();
          setTeam(teamData);
        }

        if (notesRes.ok) {
          const notesData = await notesRes.json();
          setTeamNotes(notesData.notes || []);
        }

        if (foldersRes.ok) {
          const foldersData = await foldersRes.json();
          setTeamFolders(foldersData.folders || []);
        }
      } catch (error) {
        console.error("Error fetching team data:", error);
        toast.error("Failed to load team data");
      } finally {
        setLoading(false);
      }
    };

    if (teamId) {
      fetchTeamData();
    }
  }, [teamId]);

  const handleCreateTeamNote = async () => {
    try {
      const response = await fetch(`/api/teams/${teamId}/notes`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          title: "Untitled Note",
          content: "",
          isTeamNote: true,
        }),
      });

      if (response.ok) {
        const newNote = await response.json();
        toast.success("Team note created successfully!");
        // Use the correct route for editing notes
        router.push(`/dashboard/notes/${newNote.note.id}?team=${teamId}`);
      } else {
        toast.error("Failed to create note");
      }
    } catch (error) {
      console.error("Error creating note:", error);
      toast.error("Failed to create note");
    }
  };

  const handleMoveToFolder = async (noteId, folderName) => {
    try {
      const response = await fetch(`/api/teams/${teamId}/notes/${noteId}`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          folder: folderName,
        }),
      });

      if (response.ok) {
        // Refresh notes
        const notesRes = await fetch(`/api/teams/${teamId}/notes`);
        if (notesRes.ok) {
          const notesData = await notesRes.json();
          setTeamNotes(notesData.notes || []);
        }
        toast.success(`Note moved to ${folderName}`);
      } else {
        toast.error("Failed to move note");
      }
    } catch (error) {
      console.error("Error moving note:", error);
      toast.error("Failed to move note");
    }
  };

  const filteredNotes = teamNotes.filter(
    (note) =>
      note.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      note.content.toLowerCase().includes(searchQuery.toLowerCase())
  );

  if (loading) {
    return (
      <div className="container mx-auto p-6">
        <div className="animate-pulse space-y-4">
          <div className="h-8 bg-muted rounded w-1/3"></div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {[...Array(6)].map((_, i) => (
              <div key={i} className="h-48 bg-muted rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background">
      <div className="max-w-6xl mx-auto px-8 py-6">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center gap-3 mb-4">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => router.back()}
              className="gap-2 text-muted-foreground hover:text-foreground -ml-2"
            >
              <ArrowLeft className="h-4 w-4" />
              Back
            </Button>
          </div>
          <div className="mb-6">
            <h1 className="text-3xl font-bold text-foreground mb-2">
              All Team Notes
            </h1>
            <p className="text-muted-foreground">
              {team?.name} • {filteredNotes.length}{" "}
              {filteredNotes.length === 1 ? "note" : "notes"}
            </p>
          </div>
        </div>

        {/* Search and Create */}
        <div className="flex items-center justify-between mb-8">
          <div className="relative max-w-sm">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search notes..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10 bg-background border-0 shadow-none focus-visible:ring-0 focus-visible:ring-offset-0 placeholder:text-muted-foreground/60"
            />
          </div>
          <Button
            onClick={handleCreateTeamNote}
            className="gap-2 bg-foreground text-background hover:bg-foreground/90 border-0 shadow-sm"
          >
            <Plus className="h-4 w-4" />
            Create Team Note
          </Button>
        </div>

        {/* Notes Grid */}
        {filteredNotes.length === 0 ? (
          <div className="flex flex-col items-center justify-center py-20">
            <div className="w-16 h-16 bg-muted/10 rounded-full flex items-center justify-center mb-6">
              <FileText className="h-8 w-8 text-muted-foreground/60" />
            </div>
            <h3 className="text-xl font-medium text-foreground mb-3">
              {searchQuery ? "No notes found" : "No team notes yet"}
            </h3>
            <p className="text-muted-foreground text-center mb-8 max-w-md leading-relaxed">
              {searchQuery
                ? "Try adjusting your search terms to find what you're looking for"
                : "Create your first team note to start collaborating with your team"}
            </p>
            {!searchQuery && (
              <Button
                onClick={handleCreateTeamNote}
                className="gap-2 bg-foreground text-background hover:bg-foreground/90"
              >
                <Plus className="h-4 w-4" />
                Create Team Note
              </Button>
            )}
          </div>
        ) : (
          <div className="space-y-1">
            {filteredNotes.map((note) => (
              <div
                key={note.id}
                className="group cursor-pointer bg-background hover:bg-muted/30 border border-transparent hover:border-border/50 rounded-lg p-4 transition-all duration-150"
                onClick={() =>
                  router.push(`/dashboard/notes/${note.id}?team=${teamId}`)
                }
              >
                <div className="flex items-start justify-between">
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center gap-3 mb-2">
                      <h3 className="font-medium text-foreground truncate">
                        {note.title || "Untitled Note"}
                      </h3>
                      <div className="flex items-center gap-2">
                        {note.folder && (
                          <span className="inline-flex items-center gap-1 px-2 py-0.5 bg-blue-50 text-blue-700 rounded text-xs font-medium">
                            <Folder className="h-3 w-3" />
                            {note.folder}
                          </span>
                        )}
                        <span className="inline-flex items-center gap-1 px-2 py-0.5 bg-muted/50 text-muted-foreground rounded text-xs">
                          <User className="h-3 w-3" />
                          {note.userId?.firstName ||
                            note.author?.name ||
                            "Unknown"}
                        </span>
                      </div>
                    </div>
                    <div className="flex items-center gap-4 text-sm text-muted-foreground mb-3">
                      <div className="flex items-center gap-1">
                        <Calendar className="h-3 w-3" />
                        {new Date(note.updatedAt).toLocaleDateString()}
                      </div>
                    </div>
                    <div className="text-sm text-muted-foreground leading-relaxed">
                      {note.content ? (
                        (() => {
                          const cleanText = note.content.replace(
                            /<[^>]*>/g,
                            ""
                          );
                          return (
                            cleanText.substring(0, 150) +
                            (cleanText.length > 150 ? "..." : "")
                          );
                        })()
                      ) : (
                        <span className="italic">No content yet...</span>
                      )}
                    </div>
                  </div>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button
                        variant="ghost"
                        size="sm"
                        className="h-8 w-8 p-0 opacity-0 group-hover:opacity-100 transition-opacity hover:bg-muted/50"
                        onClick={(e) => e.stopPropagation()}
                      >
                        <MoreVertical className="h-4 w-4 text-muted-foreground" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent
                      align="end"
                      className="w-52 shadow-lg border-border/50"
                    >
                      <DropdownMenuItem
                        onClick={(e) => {
                          e.stopPropagation();
                          router.push(
                            `/dashboard/notes/${note.id}?team=${teamId}`
                          );
                        }}
                        className="gap-2"
                      >
                        <FileText className="h-4 w-4" />
                        Edit Note
                      </DropdownMenuItem>
                      {teamFolders.length > 0 && (
                        <>
                          <DropdownMenuItem
                            disabled
                            className="text-xs font-medium text-muted-foreground"
                          >
                            Move to Folder
                          </DropdownMenuItem>
                          {teamFolders.map((folder) => (
                            <DropdownMenuItem
                              key={folder.id}
                              onClick={(e) => {
                                e.stopPropagation();
                                handleMoveToFolder(note.id, folder.name);
                              }}
                              className="pl-6 gap-2"
                            >
                              <Folder className="h-4 w-4" />
                              {folder.name}
                            </DropdownMenuItem>
                          ))}
                        </>
                      )}
                      {note.folder && (
                        <DropdownMenuItem
                          onClick={(e) => {
                            e.stopPropagation();
                            handleMoveToFolder(note.id, null);
                          }}
                          className="gap-2"
                        >
                          <FolderPlus className="h-4 w-4" />
                          Remove from Folder
                        </DropdownMenuItem>
                      )}
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
}
