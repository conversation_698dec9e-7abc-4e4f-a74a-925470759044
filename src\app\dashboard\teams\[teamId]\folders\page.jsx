"use client";

import React, { useState, useEffect } from "react";
import { useParams, useRouter } from "next/navigation";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import {
  Folder,
  FolderPlus,
  ArrowLeft,
  Calendar,
  FileText,
  Plus,
  Check,
  X,
  ChevronRight,
} from "lucide-react";
import { toast } from "sonner";

export default function AllTeamFoldersPage() {
  const params = useParams();
  const router = useRouter();
  const teamId = params.teamId;

  const [teamFolders, setTeamFolders] = useState([]);
  const [team, setTeam] = useState(null);
  const [loading, setLoading] = useState(true);
  const [isCreatingFolder, setIsCreatingFolder] = useState(false);
  const [newFolderName, setNewFolderName] = useState("");

  // Fetch team data
  useEffect(() => {
    const fetchTeamData = async () => {
      try {
        const [teamRes, foldersRes] = await Promise.all([
          fetch(`/api/teams/${teamId}`),
          fetch(`/api/teams/${teamId}/folders`),
        ]);

        if (teamRes.ok) {
          const teamData = await teamRes.json();
          setTeam(teamData);
        }

        if (foldersRes.ok) {
          const foldersData = await foldersRes.json();
          setTeamFolders(foldersData.folders || []);
        }
      } catch (error) {
        console.error("Error fetching team data:", error);
        toast.error("Failed to load team data");
      } finally {
        setLoading(false);
      }
    };

    if (teamId) {
      fetchTeamData();
    }
  }, [teamId]);

  const handleCreateFolder = async () => {
    if (!newFolderName.trim()) {
      toast.error("Please enter a folder name");
      return;
    }

    try {
      const response = await fetch(`/api/teams/${teamId}/folders`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          name: newFolderName.trim(),
          description: "",
          color: "#6366f1",
        }),
      });

      if (response.ok) {
        const newFolder = await response.json();
        setTeamFolders((prev) => [...prev, newFolder.folder]);
        setNewFolderName("");
        setIsCreatingFolder(false);
        toast.success("Folder created successfully");
      } else {
        const errorData = await response.json();
        toast.error(errorData.error || "Failed to create folder");
      }
    } catch (error) {
      console.error("Error creating folder:", error);
      toast.error("Failed to create folder");
    }
  };

  if (loading) {
    return (
      <div className="container mx-auto p-6">
        <div className="animate-pulse space-y-4">
          <div className="h-8 bg-muted rounded w-1/3"></div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {[...Array(6)].map((_, i) => (
              <div key={i} className="h-32 bg-muted rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center gap-4">
        <Button
          variant="ghost"
          size="sm"
          onClick={() => router.back()}
          className="gap-2"
        >
          <ArrowLeft className="h-4 w-4" />
          Back
        </Button>
        <div className="flex-1">
          <h1 className="text-2xl font-bold">All Team Folders</h1>
          <p className="text-muted-foreground">
            {team?.name} • {teamFolders.length} {teamFolders.length === 1 ? "folder" : "folders"}
          </p>
        </div>
        <Button 
          onClick={() => setIsCreatingFolder(true)} 
          className="gap-2"
        >
          <FolderPlus className="h-4 w-4" />
          Create Team Folder
        </Button>
      </div>

      {/* Folder Creation Form */}
      {isCreatingFolder && (
        <Card>
          <CardHeader>
            <CardTitle>Create New Folder</CardTitle>
            <CardDescription>
              Create a new folder to organize your team notes
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div>
                <label className="text-sm font-medium">Folder Name</label>
                <Input
                  placeholder="Enter folder name"
                  value={newFolderName}
                  onChange={(e) => setNewFolderName(e.target.value)}
                  onKeyDown={(e) => {
                    if (e.key === "Enter") {
                      handleCreateFolder();
                    } else if (e.key === "Escape") {
                      setIsCreatingFolder(false);
                      setNewFolderName("");
                    }
                  }}
                  autoFocus
                  className="mt-1"
                />
              </div>
              <div className="flex gap-2">
                <Button onClick={handleCreateFolder} className="gap-2">
                  <Check className="h-4 w-4" />
                  Create Folder
                </Button>
                <Button
                  variant="ghost"
                  onClick={() => {
                    setIsCreatingFolder(false);
                    setNewFolderName("");
                  }}
                  className="gap-2"
                >
                  <X className="h-4 w-4" />
                  Cancel
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Folders Grid */}
      {teamFolders.length === 0 ? (
        <Card>
          <CardContent className="flex flex-col items-center justify-center py-12">
            <Folder className="h-12 w-12 text-muted-foreground mb-4" />
            <h3 className="text-lg font-semibold mb-2">No team folders yet</h3>
            <p className="text-muted-foreground text-center mb-4">
              Create your first folder to organize your team notes
            </p>
            <Button 
              onClick={() => setIsCreatingFolder(true)} 
              className="gap-2"
            >
              <FolderPlus className="h-4 w-4" />
              Create Team Folder
            </Button>
          </CardContent>
        </Card>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {teamFolders.map((folder) => (
            <Card 
              key={folder.id} 
              className="hover:shadow-md transition-shadow cursor-pointer group"
              onClick={() => router.push(`/dashboard/teams/${teamId}/folders/${folder.id}`)}
            >
              <CardHeader className="pb-3">
                <div className="flex items-start justify-between">
                  <div className="flex items-center gap-3">
                    <div 
                      className="p-2 rounded-lg"
                      style={{ 
                        backgroundColor: `${folder.color || "#6366f1"}20`,
                      }}
                    >
                      <Folder 
                        className="h-5 w-5" 
                        style={{ color: folder.color || "#6366f1" }}
                      />
                    </div>
                    <div className="flex-1 min-w-0">
                      <CardTitle className="text-base truncate">
                        {folder.name}
                      </CardTitle>
                      <CardDescription className="flex items-center gap-2 mt-1">
                        <Calendar className="h-3 w-3" />
                        {new Date(folder.lastUpdated || folder.createdAt).toLocaleDateString()}
                      </CardDescription>
                    </div>
                  </div>
                  <ChevronRight className="h-4 w-4 text-muted-foreground opacity-0 group-hover:opacity-100 transition-opacity" />
                </div>
              </CardHeader>
              <CardContent className="pt-0">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2 text-sm text-muted-foreground">
                    <FileText className="h-4 w-4" />
                    <span>
                      {folder.count || 0} {folder.count === 1 ? "note" : "notes"}
                    </span>
                  </div>
                  {folder.description && (
                    <p className="text-xs text-muted-foreground truncate max-w-32">
                      {folder.description}
                    </p>
                  )}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
}
