"use client";

import { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  Di<PERSON>Header,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Search, Folder, X, Plus } from "lucide-react";
import { toast } from "sonner";

export default function MoveToFolderDialog({
  isOpen,
  onClose,
  noteTitle,
  teamId,
  noteId,
  currentFolder,
  onMoveSuccess,
}) {
  const [folders, setFolders] = useState([]);
  const [filteredFolders, setFilteredFolders] = useState([]);
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedFolder, setSelectedFolder] = useState(null);
  const [isCreatingFolder, setIsCreatingFolder] = useState(false);
  const [newFolderName, setNewFolderName] = useState("");
  const [isLoading, setIsLoading] = useState(false);

  // Fetch team folders
  useEffect(() => {
    if (isOpen && teamId) {
      fetchFolders();
    }
  }, [isOpen, teamId]);

  // Filter folders based on search
  useEffect(() => {
    if (searchQuery.trim()) {
      setFilteredFolders(
        folders.filter((folder) =>
          folder.name.toLowerCase().includes(searchQuery.toLowerCase())
        )
      );
    } else {
      setFilteredFolders(folders);
    }
  }, [searchQuery, folders]);

  const fetchFolders = async () => {
    try {
      const response = await fetch(`/api/teams/${teamId}/folders`);
      if (response.ok) {
        const data = await response.json();
        setFolders(data.folders || []);
      }
    } catch (error) {
      console.error("Error fetching folders:", error);
      toast.error("Failed to load folders");
    }
  };

  const handleMoveToFolder = async () => {
    if (!selectedFolder) {
      toast.error("Please select a folder");
      return;
    }

    setIsLoading(true);
    try {
      const response = await fetch(`/api/notes/${noteId}/move-to-folder`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          folderId: selectedFolder.id === "root" ? null : selectedFolder.id,
          folderName: selectedFolder.id === "root" ? null : selectedFolder.name,
        }),
      });

      if (response.ok) {
        const message =
          selectedFolder.id === "root"
            ? "Note removed from folder"
            : `Note moved to "${selectedFolder.name}"`;
        toast.success(message);
        onMoveSuccess?.(
          selectedFolder.id === "root" ? null : selectedFolder.name
        );
        onClose();
      } else {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to move note");
      }
    } catch (error) {
      console.error("Error moving note:", error);
      toast.error(error.message || "Failed to move note");
    } finally {
      setIsLoading(false);
    }
  };

  const handleCreateAndMove = async () => {
    if (!newFolderName.trim()) {
      toast.error("Please enter a folder name");
      return;
    }

    setIsLoading(true);
    try {
      // Create folder
      const createResponse = await fetch(`/api/teams/${teamId}/folders`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          name: newFolderName.trim(),
        }),
      });

      if (!createResponse.ok) {
        const errorData = await createResponse.json();
        throw new Error(errorData.error || "Failed to create folder");
      }

      const newFolderData = await createResponse.json();

      // Move note to new folder
      const moveResponse = await fetch(`/api/notes/${noteId}/move-to-folder`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          folderId: newFolderData.folder.id,
          folderName: newFolderData.folder.name,
        }),
      });

      if (moveResponse.ok) {
        toast.success(
          `Note moved to new folder "${newFolderData.folder.name}"`
        );
        onMoveSuccess?.(newFolderData.folder.name);
        onClose();
      } else {
        const moveErrorData = await moveResponse.json();
        throw new Error(
          moveErrorData.error || "Failed to move note to new folder"
        );
      }
    } catch (error) {
      console.error("Error creating folder and moving note:", error);
      toast.error(error.message || "Failed to create folder and move note");
    } finally {
      setIsLoading(false);
    }
  };

  const handleClose = () => {
    setSelectedFolder(null);
    setIsCreatingFolder(false);
    setNewFolderName("");
    setSearchQuery("");
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[520px] border-0 shadow-xl bg-background/98 backdrop-blur-sm animate-in fade-in-0 zoom-in-95 duration-300 ease-out">
        <DialogHeader className="space-y-0 pb-8">
          <div className="flex items-start gap-4">
            <div className="flex h-11 w-11 items-center justify-center rounded-xl bg-gradient-to-br from-primary/12 to-primary/6 border border-primary/15 shadow-sm">
              <Folder className="h-5 w-5 text-primary" />
            </div>
            <div className="flex-1 space-y-1.5">
              <DialogTitle className="text-lg font-semibold tracking-tight text-foreground">
                Move to Folder
              </DialogTitle>
              <p className="text-sm text-muted-foreground/90 leading-relaxed">
                Move{" "}
                <span className="font-medium text-foreground/95">
                  "{noteTitle}"
                </span>{" "}
                to a different folder
              </p>
            </div>
          </div>
        </DialogHeader>

        <div className="space-y-7">
          {/* Search */}
          <div className="relative group">
            <Search className="absolute left-3.5 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground/60 group-focus-within:text-primary/80 transition-colors duration-200" />
            <Input
              placeholder="Search folders..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-11 h-12 bg-muted/40 border-border/40 focus:bg-background focus:border-primary/50 focus:ring-2 focus:ring-primary/15 transition-all duration-300 ease-out placeholder:text-muted-foreground/70 text-sm font-medium"
            />
          </div>

          {/* Select Folder */}
          <div className="space-y-4">
            <h4 className="text-sm font-semibold text-foreground/95 tracking-wide">
              Select Folder
            </h4>
            <div className="space-y-2 max-h-52 overflow-y-auto scrollbar-hide">
              {filteredFolders.length > 0 ? (
                filteredFolders.map((folder) => (
                  <div
                    key={folder.id}
                    className={`group flex items-center gap-3.5 p-3.5 rounded-xl border cursor-pointer transition-all duration-300 ease-out ${
                      selectedFolder?.id === folder.id
                        ? "border-primary/60 bg-gradient-to-r from-primary/8 to-primary/4 shadow-md shadow-primary/10"
                        : "border-border/30 hover:border-border/50 hover:bg-muted/40 hover:shadow-sm"
                    }`}
                    onClick={() => setSelectedFolder(folder)}
                  >
                    <div
                      className={`flex h-9 w-9 items-center justify-center rounded-lg transition-all duration-300 ${
                        selectedFolder?.id === folder.id
                          ? "bg-primary/15 text-primary shadow-sm"
                          : "bg-muted/60 text-muted-foreground group-hover:bg-muted/80 group-hover:text-foreground/80"
                      }`}
                    >
                      <Folder className="h-4.5 w-4.5" />
                    </div>
                    <span
                      className={`text-sm font-medium transition-colors duration-300 ${
                        selectedFolder?.id === folder.id
                          ? "text-foreground"
                          : "text-foreground/85 group-hover:text-foreground"
                      }`}
                    >
                      {folder.name}
                    </span>
                    {selectedFolder?.id === folder.id && (
                      <div className="ml-auto animate-in fade-in-0 zoom-in-50 duration-200">
                        <div className="h-2.5 w-2.5 rounded-full bg-primary shadow-sm"></div>
                      </div>
                    )}
                  </div>
                ))
              ) : (
                <div className="text-center py-14 text-muted-foreground">
                  <div className="flex h-14 w-14 items-center justify-center rounded-2xl bg-gradient-to-br from-muted/40 to-muted/20 mx-auto mb-4 border border-border/30">
                    <Folder className="h-6 w-6 opacity-60" />
                  </div>
                  <p className="text-sm font-semibold text-foreground/80">
                    No folders found
                  </p>
                  <p className="text-xs text-muted-foreground/80 mt-1.5 leading-relaxed">
                    {searchQuery
                      ? "Try a different search term"
                      : "Create your first folder below"}
                  </p>
                </div>
              )}
            </div>
          </div>

          {/* Create New Folder */}
          <div className="space-y-4">
            <h4 className="text-sm font-semibold text-foreground/95 tracking-wide">
              Create New Folder
            </h4>
            {!isCreatingFolder ? (
              <Button
                variant="outline"
                onClick={() => setIsCreatingFolder(true)}
                className="w-full gap-3 h-12 border-dashed border-border/60 hover:border-primary/60 hover:bg-gradient-to-r hover:from-primary/8 hover:to-primary/4 transition-all duration-300 ease-out group"
              >
                <div className="flex h-7 w-7 items-center justify-center rounded-lg bg-primary/12 group-hover:bg-primary/20 transition-colors duration-300">
                  <Plus className="h-4 w-4 text-primary" />
                </div>
                <span className="font-medium">Create New Folder</span>
              </Button>
            ) : (
              <div className="space-y-3.5">
                <div className="relative group">
                  <Input
                    placeholder="Enter folder name..."
                    value={newFolderName}
                    onChange={(e) => setNewFolderName(e.target.value)}
                    onKeyDown={(e) => {
                      if (e.key === "Enter" && newFolderName.trim()) {
                        handleCreateAndMove();
                      } else if (e.key === "Escape") {
                        setIsCreatingFolder(false);
                        setNewFolderName("");
                      }
                    }}
                    className="h-12 pr-12 bg-muted/40 border-border/50 focus:bg-background focus:border-primary/60 focus:ring-2 focus:ring-primary/15 transition-all duration-300 ease-out placeholder:text-muted-foreground/70 font-medium"
                    autoFocus
                  />
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => {
                      setIsCreatingFolder(false);
                      setNewFolderName("");
                    }}
                    className="absolute right-2.5 top-1/2 transform -translate-y-1/2 h-7 w-7 p-0 hover:bg-muted/60 rounded-lg transition-colors duration-200"
                  >
                    <X className="h-3.5 w-3.5 text-muted-foreground/80" />
                  </Button>
                </div>
                <p className="text-xs text-muted-foreground/85 px-1 leading-relaxed">
                  Press{" "}
                  <kbd className="px-1.5 py-0.5 bg-muted/60 rounded text-xs font-medium">
                    Enter
                  </kbd>{" "}
                  to create and move, or{" "}
                  <kbd className="px-1.5 py-0.5 bg-muted/60 rounded text-xs font-medium">
                    Esc
                  </kbd>{" "}
                  to cancel
                </p>
              </div>
            )}
          </div>
        </div>

        {/* Actions */}
        <div className="flex justify-end gap-3 pt-8 border-t border-border/25">
          <Button
            variant="outline"
            onClick={handleClose}
            className="h-11 px-6 border-border/60 hover:bg-muted/60 hover:border-border/80 transition-all duration-300 ease-out font-medium"
          >
            Cancel
          </Button>
          {isCreatingFolder && newFolderName.trim() ? (
            <Button
              onClick={handleCreateAndMove}
              disabled={isLoading}
              className="h-11 px-6 bg-gradient-to-r from-primary to-primary/90 hover:from-primary/90 hover:to-primary/80 shadow-md hover:shadow-lg transition-all duration-300 ease-out font-medium disabled:opacity-60"
            >
              {isLoading ? (
                <>
                  <div className="h-4 w-4 animate-spin rounded-full border-2 border-primary-foreground/30 border-t-primary-foreground mr-2.5" />
                  Creating...
                </>
              ) : (
                <>
                  <Plus className="h-4 w-4 mr-2.5" />
                  Create & Move
                </>
              )}
            </Button>
          ) : (
            <Button
              onClick={handleMoveToFolder}
              disabled={!selectedFolder || isLoading}
              className="h-11 px-6 bg-gradient-to-r from-primary to-primary/90 hover:from-primary/90 hover:to-primary/80 shadow-md hover:shadow-lg transition-all duration-300 ease-out font-medium disabled:opacity-50 disabled:cursor-not-allowed disabled:shadow-none"
            >
              {isLoading ? (
                <>
                  <div className="h-4 w-4 animate-spin rounded-full border-2 border-primary-foreground/30 border-t-primary-foreground mr-2.5" />
                  Moving...
                </>
              ) : (
                <>
                  <Folder className="h-4 w-4 mr-2.5" />
                  Move Note
                </>
              )}
            </Button>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
}
