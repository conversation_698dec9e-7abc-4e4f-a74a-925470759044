"use client";

import { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  Di<PERSON>Header,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Search, Folder, X, Plus } from "lucide-react";
import { toast } from "sonner";

export default function MoveToFolderDialog({
  isOpen,
  onClose,
  noteTitle,
  teamId,
  noteId,
  currentFolder,
  onMoveSuccess,
}) {
  const [folders, setFolders] = useState([]);
  const [filteredFolders, setFilteredFolders] = useState([]);
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedFolder, setSelectedFolder] = useState(null);
  const [isCreatingFolder, setIsCreatingFolder] = useState(false);
  const [newFolderName, setNewFolderName] = useState("");
  const [isLoading, setIsLoading] = useState(false);

  // Fetch team folders
  useEffect(() => {
    if (isOpen && teamId) {
      fetchFolders();
    }
  }, [isOpen, teamId]);

  // Filter folders based on search
  useEffect(() => {
    if (searchQuery.trim()) {
      setFilteredFolders(
        folders.filter((folder) =>
          folder.name.toLowerCase().includes(searchQuery.toLowerCase())
        )
      );
    } else {
      setFilteredFolders(folders);
    }
  }, [searchQuery, folders]);

  const fetchFolders = async () => {
    try {
      const response = await fetch(`/api/teams/${teamId}/folders`);
      if (response.ok) {
        const data = await response.json();
        setFolders(data.folders || []);
      }
    } catch (error) {
      console.error("Error fetching folders:", error);
      toast.error("Failed to load folders");
    }
  };

  const handleMoveToFolder = async () => {
    if (!selectedFolder) {
      toast.error("Please select a folder");
      return;
    }

    setIsLoading(true);
    try {
      const response = await fetch(`/api/notes/${noteId}/move-to-folder`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          folderId: selectedFolder.id === "root" ? null : selectedFolder.id,
          folderName: selectedFolder.id === "root" ? null : selectedFolder.name,
        }),
      });

      if (response.ok) {
        const message =
          selectedFolder.id === "root"
            ? "Note removed from folder"
            : `Note moved to "${selectedFolder.name}"`;
        toast.success(message);
        onMoveSuccess?.(
          selectedFolder.id === "root" ? null : selectedFolder.name
        );
        onClose();
      } else {
        throw new Error("Failed to move note");
      }
    } catch (error) {
      console.error("Error moving note:", error);
      toast.error("Failed to move note");
    } finally {
      setIsLoading(false);
    }
  };

  const handleCreateAndMove = async () => {
    if (!newFolderName.trim()) {
      toast.error("Please enter a folder name");
      return;
    }

    setIsLoading(true);
    try {
      // Create folder
      const createResponse = await fetch(`/api/teams/${teamId}/folders`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          name: newFolderName.trim(),
        }),
      });

      if (!createResponse.ok) {
        throw new Error("Failed to create folder");
      }

      const newFolder = await createResponse.json();

      // Move note to new folder
      const moveResponse = await fetch(`/api/notes/${noteId}/move-to-folder`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          folderId: newFolder.folder.id,
          folderName: newFolder.folder.name,
        }),
      });

      if (moveResponse.ok) {
        toast.success(`Note moved to new folder "${newFolder.folder.name}"`);
        onMoveSuccess?.(newFolder.folder.name);
        onClose();
      } else {
        throw new Error("Failed to move note to new folder");
      }
    } catch (error) {
      console.error("Error creating folder and moving note:", error);
      toast.error("Failed to create folder and move note");
    } finally {
      setIsLoading(false);
    }
  };

  const handleClose = () => {
    setSelectedFolder(null);
    setIsCreatingFolder(false);
    setNewFolderName("");
    setSearchQuery("");
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <div className="flex items-center gap-2">
            <Folder className="h-5 w-5" />
            <DialogTitle>Move to Folder</DialogTitle>
          </div>
          <p className="text-sm text-muted-foreground">
            Move "{noteTitle}" to a different folder
          </p>
        </DialogHeader>

        <div className="space-y-4">
          {/* Search */}
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search folders..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>

          {/* Select Folder */}
          <div>
            <h4 className="text-sm font-medium mb-3">Select Folder</h4>
            <div className="space-y-2 max-h-48 overflow-y-auto">
              {/* Remove from folder option */}
              {currentFolder && (
                <div
                  className={`flex items-center gap-3 p-3 rounded-md border cursor-pointer transition-colors ${
                    selectedFolder?.id === "root"
                      ? "border-foreground bg-muted/20"
                      : "border-border/50 hover:bg-muted/10"
                  }`}
                  onClick={() => setSelectedFolder({ id: "root", name: null })}
                >
                  <Folder className="h-4 w-4 text-muted-foreground" />
                  <span className="text-sm">Remove from folder</span>
                </div>
              )}

              {filteredFolders.length > 0 ? (
                filteredFolders.map((folder) => (
                  <div
                    key={folder.id}
                    className={`flex items-center gap-3 p-3 rounded-md border cursor-pointer transition-colors ${
                      selectedFolder?.id === folder.id
                        ? "border-foreground bg-muted/20"
                        : "border-border/50 hover:bg-muted/10"
                    }`}
                    onClick={() => setSelectedFolder(folder)}
                  >
                    <Folder className="h-4 w-4 text-muted-foreground" />
                    <span className="text-sm">{folder.name}</span>
                  </div>
                ))
              ) : (
                <p className="text-sm text-muted-foreground text-center py-4">
                  {searchQuery ? "No folders found" : "No folders available"}
                </p>
              )}
            </div>
          </div>

          {/* Create New Folder */}
          <div>
            <h4 className="text-sm font-medium mb-3">Create New Folder</h4>
            {!isCreatingFolder ? (
              <Button
                variant="outline"
                onClick={() => setIsCreatingFolder(true)}
                className="w-full gap-2"
              >
                <Plus className="h-4 w-4" />
                Create New Folder
              </Button>
            ) : (
              <div className="space-y-2">
                <div className="relative">
                  <Input
                    placeholder="Enter folder name..."
                    value={newFolderName}
                    onChange={(e) => setNewFolderName(e.target.value)}
                    onKeyDown={(e) => {
                      if (e.key === "Enter") {
                        handleCreateAndMove();
                      } else if (e.key === "Escape") {
                        setIsCreatingFolder(false);
                        setNewFolderName("");
                      }
                    }}
                    autoFocus
                  />
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => {
                      setIsCreatingFolder(false);
                      setNewFolderName("");
                    }}
                    className="absolute right-1 top-1/2 transform -translate-y-1/2 h-6 w-6 p-0"
                  >
                    <X className="h-3 w-3" />
                  </Button>
                </div>
                <p className="text-xs text-muted-foreground">
                  Enter a folder name to create and move the note
                </p>
              </div>
            )}
          </div>
        </div>

        {/* Actions */}
        <div className="flex justify-end gap-3 pt-4">
          <Button variant="outline" onClick={handleClose}>
            Cancel
          </Button>
          {isCreatingFolder && newFolderName.trim() ? (
            <Button onClick={handleCreateAndMove} disabled={isLoading}>
              Create & Move
            </Button>
          ) : (
            <Button
              onClick={handleMoveToFolder}
              disabled={!selectedFolder || isLoading}
            >
              Move Note
            </Button>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
}
