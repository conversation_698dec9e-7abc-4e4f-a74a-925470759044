"use client";

import React, { useState, useEffect } from "react";
import { useParams, useRouter } from "next/navigation";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import {
  Folder,
  ArrowLeft,
  Plus,
  FileText,
  Search,
  MoreVertical,
  Calendar,
  User,
  FolderOpen,
} from "lucide-react";
import { toast } from "sonner";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";

export default function FolderPage() {
  const params = useParams();
  const router = useRouter();
  const { teamId, folderId } = params;

  const [folder, setFolder] = useState(null);
  const [folderNotes, setFolderNotes] = useState([]);
  const [allTeamNotes, setAllTeamNotes] = useState([]);
  const [team, setTeam] = useState(null);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState("");
  const [showAddNoteDialog, setShowAddNoteDialog] = useState(false);

  // Fetch folder data
  useEffect(() => {
    const fetchFolderData = async () => {
      try {
        const [teamRes, folderNotesRes, allNotesRes] = await Promise.all([
          fetch(`/api/teams/${teamId}`),
          fetch(
            `/api/teams/${teamId}/notes?folder=${encodeURIComponent(
              folder?.name || ""
            )}`
          ),
          fetch(`/api/teams/${teamId}/notes`),
        ]);

        if (teamRes.ok) {
          const teamData = await teamRes.json();
          setTeam(teamData);
        }

        if (folderNotesRes.ok) {
          const notesData = await folderNotesRes.json();
          setFolderNotes(notesData.notes || []);
        }

        if (allNotesRes.ok) {
          const allNotesData = await allNotesRes.json();
          setAllTeamNotes(allNotesData.notes || []);
        }
      } catch (error) {
        console.error("Error fetching folder data:", error);
        toast.error("Failed to load folder data");
      } finally {
        setLoading(false);
      }
    };

    if (teamId && folder) {
      fetchFolderData();
    }
  }, [teamId, folder]);

  // Fetch folder details
  useEffect(() => {
    const fetchFolder = async () => {
      try {
        const response = await fetch(`/api/teams/${teamId}/folders`);
        if (response.ok) {
          const data = await response.json();
          const foundFolder = data.folders.find((f) => f.id === folderId);
          if (foundFolder) {
            setFolder(foundFolder);
          } else {
            toast.error("Folder not found");
            router.push(`/dashboard/teams/${teamId}/folders`);
          }
        }
      } catch (error) {
        console.error("Error fetching folder:", error);
        toast.error("Failed to load folder");
      }
    };

    if (teamId && folderId) {
      fetchFolder();
    }
  }, [teamId, folderId, router]);

  const handleCreateTeamNote = async () => {
    try {
      const response = await fetch(`/api/teams/${teamId}/notes`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          title: "Untitled Note",
          content: "",
          isTeamNote: true,
          folder: folder.name,
        }),
      });

      if (response.ok) {
        const newNote = await response.json();
        toast.success("Team note created successfully!");
        // Use the correct route for editing notes
        router.push(`/dashboard/notes/${newNote.note.id}?team=${teamId}`);
      } else {
        toast.error("Failed to create note");
      }
    } catch (error) {
      console.error("Error creating note:", error);
      toast.error("Failed to create note");
    }
  };

  const handleAddExistingNote = async (noteId) => {
    try {
      const response = await fetch(`/api/teams/${teamId}/notes/${noteId}`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          folder: folder.name,
        }),
      });

      if (response.ok) {
        // Refresh folder notes
        const notesRes = await fetch(
          `/api/teams/${teamId}/notes?folder=${encodeURIComponent(folder.name)}`
        );
        if (notesRes.ok) {
          const notesData = await notesRes.json();
          setFolderNotes(notesData.notes || []);
        }

        // Refresh all notes
        const allNotesRes = await fetch(`/api/teams/${teamId}/notes`);
        if (allNotesRes.ok) {
          const allNotesData = await allNotesRes.json();
          setAllTeamNotes(allNotesData.notes || []);
        }

        setShowAddNoteDialog(false);
        toast.success("Note added to folder");
      } else {
        toast.error("Failed to add note to folder");
      }
    } catch (error) {
      console.error("Error adding note to folder:", error);
      toast.error("Failed to add note to folder");
    }
  };

  const handleRemoveFromFolder = async (noteId) => {
    try {
      const response = await fetch(`/api/teams/${teamId}/notes/${noteId}`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          folder: null,
        }),
      });

      if (response.ok) {
        // Refresh folder notes
        const notesRes = await fetch(
          `/api/teams/${teamId}/notes?folder=${encodeURIComponent(folder.name)}`
        );
        if (notesRes.ok) {
          const notesData = await notesRes.json();
          setFolderNotes(notesData.notes || []);
        }
        toast.success("Note removed from folder");
      } else {
        toast.error("Failed to remove note from folder");
      }
    } catch (error) {
      console.error("Error removing note from folder:", error);
      toast.error("Failed to remove note from folder");
    }
  };

  const filteredNotes = folderNotes.filter(
    (note) =>
      note.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      note.content.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const availableNotes = allTeamNotes.filter(
    (note) => !note.folder || note.folder !== folder?.name
  );

  if (loading || !folder) {
    return (
      <div className="container mx-auto p-6">
        <div className="animate-pulse space-y-4">
          <div className="h-8 bg-muted rounded w-1/3"></div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {[...Array(6)].map((_, i) => (
              <div key={i} className="h-48 bg-muted rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center gap-4">
        <Button
          variant="ghost"
          size="sm"
          onClick={() => router.push(`/dashboard/teams/${teamId}/folders`)}
          className="gap-2"
        >
          <ArrowLeft className="h-4 w-4" />
          Back to Folders
        </Button>
        <div className="flex items-center gap-3 flex-1">
          <div
            className="p-2 rounded-lg"
            style={{
              backgroundColor: `${folder.color || "#6366f1"}20`,
            }}
          >
            <FolderOpen
              className="h-6 w-6"
              style={{ color: folder.color || "#6366f1" }}
            />
          </div>
          <div>
            <h1 className="text-2xl font-bold">{folder.name}</h1>
            <p className="text-muted-foreground">
              {team?.name} • {filteredNotes.length}{" "}
              {filteredNotes.length === 1 ? "note" : "notes"}
            </p>
          </div>
        </div>
        <div className="flex gap-2">
          <Dialog open={showAddNoteDialog} onOpenChange={setShowAddNoteDialog}>
            <DialogTrigger asChild>
              <Button variant="outline" className="gap-2">
                <Plus className="h-4 w-4" />
                Add Team Note
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Add Note to {folder.name}</DialogTitle>
                <DialogDescription>
                  Select an existing note to add to this folder
                </DialogDescription>
              </DialogHeader>
              <div className="space-y-4 max-h-96 overflow-y-auto">
                {availableNotes.length === 0 ? (
                  <p className="text-muted-foreground text-center py-4">
                    No available notes to add
                  </p>
                ) : (
                  availableNotes.map((note) => (
                    <div
                      key={note.id}
                      className="flex items-center justify-between p-3 border rounded-lg hover:bg-muted/50 cursor-pointer"
                      onClick={() => handleAddExistingNote(note.id)}
                    >
                      <div>
                        <p className="font-medium">
                          {note.title || "Untitled Note"}
                        </p>
                        <p className="text-sm text-muted-foreground truncate">
                          {note.content || "No content..."}
                        </p>
                      </div>
                      <Button size="sm" variant="ghost">
                        Add
                      </Button>
                    </div>
                  ))
                )}
              </div>
            </DialogContent>
          </Dialog>
          <Button onClick={handleCreateTeamNote} className="gap-2">
            <Plus className="h-4 w-4" />
            Create New Note
          </Button>
        </div>
      </div>

      {/* Search */}
      <div className="relative max-w-md">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
        <Input
          placeholder="Search notes in this folder..."
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          className="pl-10"
        />
      </div>

      {/* Notes Grid */}
      {filteredNotes.length === 0 ? (
        <Card>
          <CardContent className="flex flex-col items-center justify-center py-12">
            <FileText className="h-12 w-12 text-muted-foreground mb-4" />
            <h3 className="text-lg font-semibold mb-2">
              {searchQuery ? "No notes found" : "No notes in this folder yet"}
            </h3>
            <p className="text-muted-foreground text-center mb-4">
              {searchQuery
                ? "Try adjusting your search terms"
                : "Create a new note or add existing notes to this folder"}
            </p>
            {!searchQuery && (
              <div className="flex gap-2">
                <Button onClick={handleCreateTeamNote} className="gap-2">
                  <Plus className="h-4 w-4" />
                  Create New Note
                </Button>
                <Button
                  variant="outline"
                  onClick={() => setShowAddNoteDialog(true)}
                  className="gap-2"
                >
                  <Plus className="h-4 w-4" />
                  Add Existing Note
                </Button>
              </div>
            )}
          </CardContent>
        </Card>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {filteredNotes.map((note) => (
            <Card key={note.id} className="hover:shadow-md transition-shadow">
              <CardHeader className="pb-3">
                <div className="flex items-start justify-between">
                  <div className="flex-1 min-w-0">
                    <CardTitle className="text-base truncate">
                      {note.title || "Untitled Note"}
                    </CardTitle>
                    <CardDescription className="flex items-center gap-2 mt-1">
                      <Calendar className="h-3 w-3" />
                      {new Date(note.updatedAt).toLocaleDateString()}
                    </CardDescription>
                  </div>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                        <MoreVertical className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem
                        onClick={() =>
                          router.push(
                            `/dashboard/notes/${note.id}?team=${teamId}`
                          )
                        }
                      >
                        Edit Note
                      </DropdownMenuItem>
                      <DropdownMenuItem
                        onClick={() => handleRemoveFromFolder(note.id)}
                      >
                        Remove from Folder
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
              </CardHeader>
              <CardContent className="pt-0">
                <p className="text-sm text-muted-foreground line-clamp-3">
                  {note.content || "No content yet..."}
                </p>
                <div className="flex items-center gap-2 mt-3">
                  <Badge variant="outline" className="text-xs">
                    <User className="h-3 w-3 mr-1" />
                    {note.author?.name || "Unknown"}
                  </Badge>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
}
