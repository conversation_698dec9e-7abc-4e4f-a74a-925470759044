"use client";

import React, { useState, useEffect } from "react";
import { useParams, useRouter } from "next/navigation";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Folder,
  ArrowLeft,
  Plus,
  FileText,
  Search,
  MoreVertical,
  Calendar,
  User,
  FolderOpen,
} from "lucide-react";
import { toast } from "sonner";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";

export default function FolderPage() {
  const params = useParams();
  const router = useRouter();
  const { teamId, folderId } = params;

  const [folder, setFolder] = useState(null);
  const [folderNotes, setFolderNotes] = useState([]);
  const [allTeamNotes, setAllTeamNotes] = useState([]);
  const [team, setTeam] = useState(null);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState("");
  const [showAddNoteDialog, setShowAddNoteDialog] = useState(false);

  // Fetch folder data
  useEffect(() => {
    const fetchFolderData = async () => {
      try {
        const [teamRes, folderNotesRes, allNotesRes] = await Promise.all([
          fetch(`/api/teams/${teamId}`),
          fetch(
            `/api/teams/${teamId}/notes?folder=${encodeURIComponent(
              folder?.name || ""
            )}`
          ),
          fetch(`/api/teams/${teamId}/notes`),
        ]);

        if (teamRes.ok) {
          const teamData = await teamRes.json();
          setTeam(teamData);
        }

        if (folderNotesRes.ok) {
          const notesData = await folderNotesRes.json();
          setFolderNotes(notesData.notes || []);
        }

        if (allNotesRes.ok) {
          const allNotesData = await allNotesRes.json();
          setAllTeamNotes(allNotesData.notes || []);
        }
      } catch (error) {
        console.error("Error fetching folder data:", error);
        toast.error("Failed to load folder data");
      } finally {
        setLoading(false);
      }
    };

    if (teamId && folder) {
      fetchFolderData();
    }
  }, [teamId, folder]);

  // Fetch folder details
  useEffect(() => {
    const fetchFolder = async () => {
      try {
        const response = await fetch(`/api/teams/${teamId}/folders`);
        if (response.ok) {
          const data = await response.json();
          const foundFolder = data.folders.find((f) => f.id === folderId);
          if (foundFolder) {
            setFolder(foundFolder);
          } else {
            toast.error("Folder not found");
            router.push(`/dashboard/teams/${teamId}/folders`);
          }
        }
      } catch (error) {
        console.error("Error fetching folder:", error);
        toast.error("Failed to load folder");
      }
    };

    if (teamId && folderId) {
      fetchFolder();
    }
  }, [teamId, folderId, router]);

  const handleCreateTeamNote = async () => {
    try {
      const response = await fetch(`/api/teams/${teamId}/notes`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          title: "Untitled Note",
          content: "",
          isTeamNote: true,
          folder: folder.name,
        }),
      });

      if (response.ok) {
        const newNote = await response.json();
        toast.success("Team note created successfully!");
        // Use the correct route for editing notes
        router.push(`/dashboard/notes/${newNote.note.id}?team=${teamId}`);
      } else {
        toast.error("Failed to create note");
      }
    } catch (error) {
      console.error("Error creating note:", error);
      toast.error("Failed to create note");
    }
  };

  const handleAddExistingNote = async (noteId) => {
    try {
      const response = await fetch(`/api/teams/${teamId}/notes/${noteId}`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          folder: folder.name,
        }),
      });

      if (response.ok) {
        // Refresh folder notes
        const notesRes = await fetch(
          `/api/teams/${teamId}/notes?folder=${encodeURIComponent(folder.name)}`
        );
        if (notesRes.ok) {
          const notesData = await notesRes.json();
          setFolderNotes(notesData.notes || []);
        }

        // Refresh all notes
        const allNotesRes = await fetch(`/api/teams/${teamId}/notes`);
        if (allNotesRes.ok) {
          const allNotesData = await allNotesRes.json();
          setAllTeamNotes(allNotesData.notes || []);
        }

        setShowAddNoteDialog(false);
        toast.success("Note added to folder");
      } else {
        toast.error("Failed to add note to folder");
      }
    } catch (error) {
      console.error("Error adding note to folder:", error);
      toast.error("Failed to add note to folder");
    }
  };

  const handleRemoveFromFolder = async (noteId) => {
    try {
      const response = await fetch(`/api/teams/${teamId}/notes/${noteId}`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          folder: null,
        }),
      });

      if (response.ok) {
        // Refresh folder notes
        const notesRes = await fetch(
          `/api/teams/${teamId}/notes?folder=${encodeURIComponent(folder.name)}`
        );
        if (notesRes.ok) {
          const notesData = await notesRes.json();
          setFolderNotes(notesData.notes || []);
        }
        toast.success("Note removed from folder");
      } else {
        toast.error("Failed to remove note from folder");
      }
    } catch (error) {
      console.error("Error removing note from folder:", error);
      toast.error("Failed to remove note from folder");
    }
  };

  const filteredNotes = folderNotes.filter(
    (note) =>
      note.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      note.content.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const availableNotes = allTeamNotes.filter(
    (note) => !note.folder || note.folder !== folder?.name
  );

  if (loading || !folder) {
    return (
      <div className="min-h-screen bg-background">
        <div className="max-w-4xl mx-auto px-6 py-8">
          <div className="animate-pulse">
            {/* Header skeleton */}
            <div className="mb-12">
              <div className="flex items-center gap-2 mb-8 -ml-2">
                <div className="h-4 w-4 bg-muted rounded"></div>
                <div className="h-4 w-20 bg-muted rounded"></div>
              </div>
              <div className="flex items-center justify-between mb-8">
                <div className="flex items-center gap-3">
                  <div className="h-10 w-10 bg-muted rounded-lg"></div>
                  <div>
                    <div className="h-8 w-32 bg-muted rounded mb-2"></div>
                    <div className="h-4 w-24 bg-muted rounded"></div>
                  </div>
                </div>
                <div className="flex gap-2">
                  <div className="h-9 w-28 bg-muted rounded"></div>
                  <div className="h-9 w-32 bg-muted rounded"></div>
                </div>
              </div>
            </div>
            {/* Search skeleton */}
            <div className="mb-8">
              <div className="h-10 w-80 bg-muted rounded"></div>
            </div>
            {/* Notes skeleton */}
            <div className="space-y-2">
              {[...Array(3)].map((_, i) => (
                <div
                  key={i}
                  className="bg-background border border-border/50 rounded-md p-5"
                >
                  <div className="flex items-start justify-between mb-3">
                    <div className="flex-1">
                      <div className="h-5 w-32 bg-muted rounded mb-2"></div>
                      <div className="flex items-center gap-3">
                        <div className="h-3 w-20 bg-muted rounded"></div>
                        <div className="h-3 w-16 bg-muted rounded"></div>
                      </div>
                    </div>
                    <div className="h-6 w-6 bg-muted rounded"></div>
                  </div>
                  <div className="space-y-1">
                    <div className="h-4 w-full bg-muted rounded"></div>
                    <div className="h-4 w-3/4 bg-muted rounded"></div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background">
      <div className="max-w-4xl mx-auto px-6 py-8">
        {/* Header */}
        <div className="mb-12">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => router.push(`/dashboard/teams/${teamId}/folders`)}
            className="gap-2 text-muted-foreground hover:text-foreground mb-8 -ml-2"
          >
            <ArrowLeft className="h-4 w-4" />
            Back to Folders
          </Button>

          <div className="flex items-center justify-between mb-8">
            <div className="flex items-center gap-3">
              <div className="flex h-10 w-10 items-center justify-center rounded-lg bg-primary/10">
                <FolderOpen className="h-5 w-5 text-primary" />
              </div>
              <div>
                <h1 className="text-2xl font-semibold text-foreground mb-2">
                  {folder.name}
                </h1>
                <p className="text-sm text-muted-foreground">
                  {team?.name} • {filteredNotes.length}{" "}
                  {filteredNotes.length === 1 ? "note" : "notes"}
                </p>
              </div>
            </div>
            <div className="flex gap-2">
              <Dialog
                open={showAddNoteDialog}
                onOpenChange={setShowAddNoteDialog}
              >
                <DialogTrigger asChild>
                  <Button variant="outline" className="gap-2">
                    <Plus className="h-4 w-4" />
                    Add Team Note
                  </Button>
                </DialogTrigger>
                <DialogContent>
                  <DialogHeader>
                    <DialogTitle>Add Note to {folder.name}</DialogTitle>
                    <DialogDescription>
                      Select an existing note to add to this folder
                    </DialogDescription>
                  </DialogHeader>
                  <div className="space-y-4 max-h-96 overflow-y-auto">
                    {availableNotes.length === 0 ? (
                      <p className="text-muted-foreground text-center py-4">
                        No available notes to add
                      </p>
                    ) : (
                      availableNotes.map((note) => (
                        <div
                          key={note.id}
                          className="flex items-center justify-between p-3 border rounded-lg hover:bg-muted/50 cursor-pointer"
                          onClick={() => handleAddExistingNote(note.id)}
                        >
                          <div>
                            <p className="font-medium">
                              {note.title || "Untitled Note"}
                            </p>
                            <p className="text-sm text-muted-foreground truncate">
                              {note.content || "No content..."}
                            </p>
                          </div>
                          <Button size="sm" variant="ghost">
                            Add
                          </Button>
                        </div>
                      ))
                    )}
                  </div>
                </DialogContent>
              </Dialog>
              <Button onClick={handleCreateTeamNote} className="gap-2">
                <Plus className="h-4 w-4" />
                Create New Note
              </Button>
            </div>
          </div>
        </div>

        {/* Search */}
        <div className="mb-8">
          <div className="relative max-w-md">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search notes in this folder..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10 bg-background border border-border/50 focus:border-border"
            />
          </div>
        </div>

        {/* Notes List */}
        {filteredNotes.length === 0 ? (
          <div className="flex flex-col items-center justify-center py-24">
            <div className="w-12 h-12 bg-muted/20 rounded-lg flex items-center justify-center mb-4">
              <FileText className="h-6 w-6 text-muted-foreground/60" />
            </div>
            <h3 className="text-lg font-medium text-foreground mb-2">
              {searchQuery ? "No notes found" : "No notes in this folder yet"}
            </h3>
            <p className="text-sm text-muted-foreground text-center mb-6 max-w-sm">
              {searchQuery
                ? "Try adjusting your search terms"
                : "Create a new note or add existing notes to this folder"}
            </p>
            {!searchQuery && (
              <div className="flex gap-2">
                <Button onClick={handleCreateTeamNote} className="gap-2">
                  <Plus className="h-4 w-4" />
                  Create New Note
                </Button>
                <Button
                  variant="outline"
                  onClick={() => setShowAddNoteDialog(true)}
                  className="gap-2"
                >
                  <Plus className="h-4 w-4" />
                  Add Existing Note
                </Button>
              </div>
            )}
          </div>
        ) : (
          <div className="space-y-2">
            {filteredNotes.map((note) => (
              <div
                key={note.id}
                className="group cursor-pointer bg-background hover:bg-muted/20 border border-border/50 hover:border-border/70 rounded-md p-5 transition-all duration-200"
                onClick={() =>
                  router.push(`/dashboard/notes/${note.id}?team=${teamId}`)
                }
              >
                <div className="flex items-start justify-between">
                  <div className="flex-1 min-w-0">
                    <div className="mb-3">
                      <h3 className="font-medium text-foreground mb-2 line-clamp-1">
                        {note.title || "Untitled Note"}
                      </h3>
                      <div className="flex items-center gap-3 text-xs text-muted-foreground">
                        <span className="flex items-center gap-1">
                          <User className="h-3 w-3" />
                          {note.userId?.firstName ||
                            note.author?.name ||
                            "Unknown"}
                        </span>
                        <span className="flex items-center gap-1">
                          <Calendar className="h-3 w-3" />
                          {new Date(note.updatedAt).toLocaleDateString()}
                        </span>
                      </div>
                    </div>
                    <div className="text-sm text-muted-foreground leading-relaxed line-clamp-2">
                      {note.content ? (
                        (() => {
                          const cleanText = note.content.replace(
                            /<[^>]*>/g,
                            ""
                          );
                          return cleanText || "No content";
                        })()
                      ) : (
                        <span className="italic">No content</span>
                      )}
                    </div>
                  </div>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button
                        variant="ghost"
                        size="sm"
                        className="h-7 w-7 p-0 opacity-0 group-hover:opacity-100 transition-opacity hover:bg-muted/50 shrink-0"
                        onClick={(e) => e.stopPropagation()}
                      >
                        <MoreVertical className="h-3.5 w-3.5 text-muted-foreground" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem
                        onClick={(e) => {
                          e.stopPropagation();
                          router.push(
                            `/dashboard/notes/${note.id}?team=${teamId}`
                          );
                        }}
                      >
                        Edit Note
                      </DropdownMenuItem>
                      <DropdownMenuItem
                        onClick={(e) => {
                          e.stopPropagation();
                          handleRemoveFromFolder(note.id);
                        }}
                      >
                        Remove from Folder
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
}
