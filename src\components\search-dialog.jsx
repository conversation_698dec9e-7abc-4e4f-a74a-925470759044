"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import {
  Search,
  Clock,
  FileText,
  X,
  Hash,
  Calendar,
  Folder,
  Trash2,
  Star,
} from "lucide-react";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";

export function SearchDialog({ open, onOpenChange }) {
  const [searchQuery, setSearchQuery] = useState("");
  const [notes, setNotes] = useState([]);
  const [folders, setFolders] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [recentSearches, setRecentSearches] = useState([]);
  const router = useRouter();

  // Fetch real notes data
  useEffect(() => {
    if (open) {
      fetchNotes();
      fetchFolders();
      loadRecentSearches();
    }
  }, [open]);

  const fetchNotes = async () => {
    try {
      setIsLoading(true);
      const response = await fetch("/api/notes");
      if (response.ok) {
        const data = await response.json();
        setNotes(data.notes || []);
      }
    } catch (error) {
      console.error("Error fetching notes:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const fetchFolders = async () => {
    try {
      const response = await fetch("/api/folders");
      if (response.ok) {
        const data = await response.json();
        setFolders(data.folders || []);
      }
    } catch (error) {
      console.error("Error fetching folders:", error);
    }
  };

  const loadRecentSearches = () => {
    // Load from localStorage
    try {
      const saved = localStorage.getItem("smartnotes-recent-searches");
      if (saved) {
        setRecentSearches(JSON.parse(saved));
      }
    } catch (error) {
      console.error("Error loading recent searches:", error);
    }
  };

  const saveRecentSearch = (query) => {
    if (!query.trim()) return;

    try {
      const newSearch = {
        query: query.trim(),
        timestamp: Date.now(),
        type: query.startsWith("#")
          ? "tag"
          : query.includes(":")
          ? "filter"
          : "text",
      };

      const updated = [
        newSearch,
        ...recentSearches.filter((s) => s.query !== query.trim()),
      ].slice(0, 10);
      setRecentSearches(updated);
      localStorage.setItem(
        "smartnotes-recent-searches",
        JSON.stringify(updated)
      );
    } catch (error) {
      console.error("Error saving recent search:", error);
    }
  };

  // Smart search suggestions
  const searchSuggestions = [
    { icon: Hash, label: "#tags", description: "Search by tags" },
    { icon: Calendar, label: "date:", description: "Filter by date" },
    { icon: Folder, label: "folder:", description: "Search in folder" },
    { icon: Star, label: "starred:", description: "Show starred notes" },
  ];

  // Enhanced search filtering for real notes
  const filteredNotes = searchQuery
    ? notes.filter((note) => {
        const query = searchQuery.toLowerCase();

        // Basic text search
        const titleMatch = note.title?.toLowerCase().includes(query);

        // Clean content for searching (strip HTML)
        const cleanContent = stripHtml(note.content || "");
        const contentMatch = cleanContent.toLowerCase().includes(query);

        // Folder search
        const folderMatch = note.folder?.toLowerCase().includes(query);

        // Tag search (if note has tags)
        const tagMatch = note.tags?.some((tag) =>
          tag.toLowerCase().includes(query)
        );

        // Special search patterns
        if (query.startsWith("#")) {
          const tagQuery = query.slice(1);
          return note.tags?.some((tag) => tag.toLowerCase().includes(tagQuery));
        }

        if (query.startsWith("folder:")) {
          const folderQuery = query.slice(7);
          return note.folder?.toLowerCase().includes(folderQuery);
        }

        if (query === "starred:" || query === "starred") {
          return note.starred === true;
        }

        return titleMatch || contentMatch || folderMatch || tagMatch;
      })
    : notes.slice(0, 8); // Show recent notes when not searching

  // Helper functions
  const removeRecentSearch = (searchToRemove) => {
    try {
      const updated = recentSearches.filter((s) => s.query !== searchToRemove);
      setRecentSearches(updated);
      localStorage.setItem(
        "smartnotes-recent-searches",
        JSON.stringify(updated)
      );
    } catch (error) {
      console.error("Error removing recent search:", error);
    }
  };

  const handleSuggestionClick = (suggestion) => {
    setSearchQuery(suggestion.label);
  };

  const handleRecentSearchClick = (search) => {
    setSearchQuery(search.query);
    saveRecentSearch(search.query);
  };

  const handleNoteClick = (note) => {
    // Save search query if there was one
    if (searchQuery.trim()) {
      saveRecentSearch(searchQuery);
    }

    // Navigate to note
    router.push(`/dashboard/notes/${note.id}`);
    onOpenChange(false);
  };

  // Format date helper
  const formatDate = (dateString) => {
    try {
      const date = new Date(dateString);
      const now = new Date();
      const diffTime = Math.abs(now - date);
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

      if (diffDays === 1) return "Today";
      if (diffDays === 2) return "Yesterday";
      if (diffDays <= 7) return `${diffDays - 1} days ago`;

      return date.toLocaleDateString();
    } catch {
      return dateString || "Unknown";
    }
  };

  // Strip HTML tags from content for display
  const stripHtml = (html) => {
    if (!html) return "";

    // Create a temporary div element to parse HTML
    const tempDiv = document.createElement("div");
    tempDiv.innerHTML = html;

    // Get text content and clean up extra whitespace
    const textContent = tempDiv.textContent || tempDiv.innerText || "";
    return textContent.replace(/\s+/g, " ").trim();
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent
        className="max-w-2xl w-[95vw] sm:w-[90vw] h-[600px] p-0 gap-0 bg-background/95 backdrop-blur-md border-border/50 rounded-xl shadow-2xl"
        showCloseButton={false}
      >
        <div className="p-5">
          <DialogTitle className="sr-only">Search Notes</DialogTitle>

          {/* Enhanced Search Input */}
          <div className="relative mb-6">
            <div className="absolute inset-0 bg-gradient-to-r from-primary/10 to-accent/10 rounded-xl blur-xl opacity-50" />
            <div className="relative bg-background/80 backdrop-blur-sm rounded-xl border-2 border-border/50 hover:border-primary/30 focus-within:border-primary/50 transition-all duration-300 shadow-lg">
              <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-muted-foreground" />
              <Input
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                placeholder="Search notes, tags, folders..."
                className="pl-12 pr-12 h-14 text-base bg-transparent border-0 focus-visible:ring-0 placeholder:text-muted-foreground/60 font-medium"
                autoFocus
                onKeyDown={(e) => {
                  if (e.key === "Escape" && searchQuery) {
                    setSearchQuery("");
                  }
                }}
              />
              {/* Clear button - only show when there's text */}
              {searchQuery && (
                <Button
                  variant="ghost"
                  size="sm"
                  className="absolute right-2 top-1/2 transform -translate-y-1/2 h-10 w-10 rounded-lg hover:bg-muted/50 transition-all duration-200"
                  onClick={() => setSearchQuery("")}
                  title="Clear search"
                >
                  <X className="h-4 w-4" />
                </Button>
              )}
            </div>
          </div>

          <ScrollArea className="h-[480px]">
            {searchQuery ? (
              // Enhanced Search Results
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <h3 className="text-sm font-semibold text-foreground flex items-center gap-2">
                    <FileText className="h-4 w-4 text-primary" />
                    Results for "{searchQuery}"
                  </h3>
                  <Badge variant="secondary" className="text-xs">
                    {filteredNotes.length} found
                  </Badge>
                </div>

                <div className="space-y-3">
                  {isLoading ? (
                    // Loading state
                    <div className="space-y-3">
                      {[...Array(3)].map((_, i) => (
                        <div
                          key={i}
                          className="bg-background/60 backdrop-blur-sm border border-border/50 rounded-lg p-4"
                        >
                          <div className="animate-pulse">
                            <div className="h-4 bg-muted rounded w-3/4 mb-2"></div>
                            <div className="h-3 bg-muted rounded w-full mb-1"></div>
                            <div className="h-3 bg-muted rounded w-2/3 mb-3"></div>
                            <div className="flex gap-2">
                              <div className="h-5 bg-muted rounded w-16"></div>
                              <div className="h-5 bg-muted rounded w-12"></div>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : filteredNotes.length > 0 ? (
                    filteredNotes.slice(0, 8).map((note) => (
                      <div
                        key={note.id}
                        onClick={() => handleNoteClick(note)}
                        className="group relative bg-background/60 backdrop-blur-sm border border-border/50 rounded-lg p-4 cursor-pointer transition-all duration-300 hover:scale-[1.02] hover:shadow-lg hover:border-primary/30 hover:bg-primary/5"
                      >
                        <div className="flex items-start justify-between gap-3">
                          <div className="flex-1 min-w-0">
                            <div className="flex items-center gap-2 mb-2">
                              <h4 className="font-semibold text-sm truncate group-hover:text-primary transition-colors">
                                {note.title || "Untitled Note"}
                              </h4>
                              {note.starred && (
                                <Star className="h-3 w-3 text-yellow-500 fill-yellow-500" />
                              )}
                            </div>
                            <p className="text-sm text-muted-foreground line-clamp-2 mb-3 leading-relaxed">
                              {note.content
                                ? (() => {
                                    const cleanContent = stripHtml(
                                      note.content
                                    );
                                    return cleanContent.length > 150
                                      ? cleanContent.substring(0, 150) + "..."
                                      : cleanContent;
                                  })()
                                : "No content"}
                            </p>
                            <div className="flex items-center gap-2 flex-wrap">
                              {note.folder && (
                                <Badge variant="outline" className="text-xs">
                                  <Folder className="h-3 w-3 mr-1" />
                                  {note.folder}
                                </Badge>
                              )}
                              {note.tags &&
                                note.tags.length > 0 &&
                                note.tags.map((tag) => (
                                  <Badge
                                    key={tag}
                                    variant="secondary"
                                    className="text-xs"
                                  >
                                    <Hash className="h-3 w-3 mr-1" />
                                    {tag}
                                  </Badge>
                                ))}
                            </div>
                          </div>
                          <div className="flex flex-col items-end gap-2">
                            <span className="text-xs text-muted-foreground/70 font-medium">
                              {formatDate(note.updatedAt || note.createdAt)}
                            </span>
                          </div>
                        </div>
                      </div>
                    ))
                  ) : (
                    <div className="text-center py-12">
                      <div className="bg-muted/30 rounded-full p-4 w-16 h-16 mx-auto mb-4 flex items-center justify-center">
                        <FileText className="h-8 w-8 text-muted-foreground/50" />
                      </div>
                      <h3 className="font-medium text-sm mb-2">
                        No notes found
                      </h3>
                      <p className="text-xs text-muted-foreground">
                        Try adjusting your search terms or create a new note
                      </p>
                    </div>
                  )}
                </div>
              </div>
            ) : (
              // Enhanced Recent Searches and Notes
              <div className="space-y-6">
                {/* Smart Search Suggestions */}
                <div className="bg-gradient-to-r from-primary/5 to-accent/5 rounded-lg p-4 border border-border/30">
                  <h3 className="text-sm font-semibold text-foreground mb-3 flex items-center gap-2">
                    <Search className="h-4 w-4 text-primary" />
                    Quick Search
                  </h3>
                  <div className="grid grid-cols-2 gap-2">
                    {searchSuggestions.map((suggestion, index) => (
                      <button
                        key={index}
                        onClick={() => handleSuggestionClick(suggestion)}
                        className="group flex items-center gap-2 p-3 bg-background/60 backdrop-blur-sm border border-border/50 rounded-lg hover:border-primary/30 hover:bg-primary/5 transition-all duration-200 hover:scale-105"
                      >
                        <suggestion.icon className="h-4 w-4 text-muted-foreground group-hover:text-primary transition-colors" />
                        <div className="text-left">
                          <div className="text-xs font-medium group-hover:text-primary transition-colors">
                            {suggestion.label}
                          </div>
                          <div className="text-xs text-muted-foreground">
                            {suggestion.description}
                          </div>
                        </div>
                      </button>
                    ))}
                  </div>
                </div>

                {/* Recent Searches Card */}
                <div className="bg-background/60 backdrop-blur-sm border border-border/50 rounded-lg p-4">
                  <div className="flex items-center justify-between mb-4">
                    <h3 className="text-sm font-semibold text-foreground flex items-center gap-2">
                      <Clock className="h-4 w-4 text-primary" />
                      Recent Searches
                    </h3>
                    <Badge variant="outline" className="text-xs">
                      {recentSearches.length}
                    </Badge>
                  </div>

                  {recentSearches.length > 0 ? (
                    <div className="space-y-2">
                      {recentSearches.map((search, index) => (
                        <div
                          key={index}
                          className="group flex items-center justify-between p-3 bg-muted/30 rounded-lg hover:bg-muted/50 transition-colors cursor-pointer"
                          onClick={() => handleRecentSearchClick(search)}
                        >
                          <div className="flex items-center gap-3">
                            <div className="p-1.5 bg-primary/10 rounded-md">
                              {search.type === "tag" ? (
                                <Hash className="h-3 w-3 text-primary" />
                              ) : search.type === "filter" ? (
                                <Folder className="h-3 w-3 text-primary" />
                              ) : (
                                <Search className="h-3 w-3 text-primary" />
                              )}
                            </div>
                            <div>
                              <div className="text-sm font-medium group-hover:text-primary transition-colors">
                                {search.query}
                              </div>
                              <div className="text-xs text-muted-foreground">
                                {search.count} results
                              </div>
                            </div>
                          </div>
                          <Button
                            variant="ghost"
                            size="sm"
                            className="opacity-0 group-hover:opacity-100 h-6 w-6 p-0 hover:bg-destructive/10 hover:text-destructive transition-all"
                            onClick={(e) => {
                              e.stopPropagation();
                              removeRecentSearch(search.query);
                            }}
                          >
                            <Trash2 className="h-3 w-3" />
                          </Button>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="text-center py-6 text-muted-foreground">
                      <Clock className="h-6 w-6 mx-auto mb-2 opacity-50" />
                      <p className="text-xs">No recent searches</p>
                    </div>
                  )}
                </div>

                <Separator className="my-6" />

                {/* Recent Notes Card */}
                <div className="bg-background/60 backdrop-blur-sm border border-border/50 rounded-lg p-4">
                  <div className="flex items-center justify-between mb-4">
                    <h3 className="text-sm font-semibold text-foreground flex items-center gap-2">
                      <FileText className="h-4 w-4 text-primary" />
                      Recent Notes
                    </h3>
                    <Badge variant="outline" className="text-xs">
                      {notes.length}
                    </Badge>
                  </div>

                  <div className="space-y-3">
                    {notes.length > 0 ? (
                      notes.slice(0, 4).map((note) => (
                        <div
                          key={note.id}
                          onClick={() => handleNoteClick(note)}
                          className="group relative bg-muted/20 border border-border/30 rounded-lg p-3 cursor-pointer transition-all duration-200 hover:scale-[1.02] hover:shadow-md hover:border-primary/30 hover:bg-primary/5"
                        >
                          <div className="flex items-start justify-between gap-3">
                            <div className="flex-1 min-w-0">
                              <div className="flex items-center gap-2 mb-1">
                                <h4 className="font-medium text-sm truncate group-hover:text-primary transition-colors">
                                  {note.title || "Untitled Note"}
                                </h4>
                                {note.starred && (
                                  <Star className="h-3 w-3 text-yellow-500 fill-yellow-500" />
                                )}
                              </div>
                              <p className="text-xs text-muted-foreground line-clamp-1 mb-2">
                                {note.content
                                  ? (() => {
                                      const cleanContent = stripHtml(
                                        note.content
                                      );
                                      return cleanContent.length > 100
                                        ? cleanContent.substring(0, 100) + "..."
                                        : cleanContent;
                                    })()
                                  : "No content"}
                              </p>
                              <div className="flex items-center gap-1">
                                {note.folder && (
                                  <Badge variant="outline" className="text-xs">
                                    {note.folder}
                                  </Badge>
                                )}
                              </div>
                            </div>
                            <span className="text-xs text-muted-foreground/70 font-medium">
                              {formatDate(note.updatedAt || note.createdAt)}
                            </span>
                          </div>
                        </div>
                      ))
                    ) : (
                      <div className="text-center py-6 text-muted-foreground">
                        <FileText className="h-6 w-6 mx-auto mb-2 opacity-50" />
                        <p className="text-xs">No notes found</p>
                        <p className="text-xs">
                          Create your first note to get started
                        </p>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            )}
          </ScrollArea>
        </div>
      </DialogContent>
    </Dialog>
  );
}
