/* Tiptap Editor Styles - Exact Template Match */
.tiptap-editor {
  font-family: ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont,
    "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", <PERSON><PERSON>, "Noto Sans", sans-serif;
  font-size: 16px;
  line-height: 1.7;
  color: hsl(var(--foreground));
}

.tiptap-editor .ProseMirror {
  outline: none;
  padding: 0;
  min-height: 500px;
}

.tiptap-editor .ProseMirror p {
  margin: 0 0 1em 0;
}

.tiptap-editor .ProseMirror h1 {
  font-size: 2.25em;
  font-weight: 700;
  line-height: 1.2;
  margin: 0 0 0.5em 0;
}

.tiptap-editor .ProseMirror h2 {
  font-size: 1.875em;
  font-weight: 600;
  line-height: 1.3;
  margin: 1.25em 0 0.5em 0;
}

.tiptap-editor .ProseMirror h3 {
  font-size: 1.5em;
  font-weight: 600;
  line-height: 1.4;
  margin: 1em 0 0.5em 0;
}

.tiptap-editor .ProseMirror strong {
  font-weight: 700;
}

.tiptap-editor .ProseMirror em {
  font-style: italic;
}

.tiptap-editor .ProseMirror u {
  text-decoration: underline;
}

.tiptap-editor .ProseMirror s {
  text-decoration: line-through;
}

.tiptap-editor .ProseMirror code {
  background: hsl(var(--muted));
  color: hsl(var(--foreground));
  padding: 0.2em 0.4em;
  border-radius: 0.25em;
  font-family: ui-monospace, SFMono-Regular, "SF Mono", Consolas,
    "Liberation Mono", Menlo, monospace;
  font-size: 0.875em;
}

.tiptap-editor .ProseMirror mark {
  background: #fbbf24;
  color: #000;
  padding: 0.1em 0.2em;
  border-radius: 0.25em;
}

.tiptap-editor .ProseMirror ul {
  list-style-type: disc;
  margin: 1em 0;
  padding-left: 1.5em;
}

.tiptap-editor .ProseMirror ol {
  list-style-type: decimal;
  margin: 1em 0;
  padding-left: 1.5em;
}

.tiptap-editor .ProseMirror li {
  margin: 0.25em 0;
}

.tiptap-editor .ProseMirror blockquote {
  border-left: 4px solid hsl(var(--border));
  padding-left: 1em;
  margin: 1em 0;
  font-style: italic;
  color: hsl(var(--muted-foreground));
}

.tiptap-editor .ProseMirror pre {
  background: hsl(var(--muted));
  color: hsl(var(--foreground));
  padding: 1em;
  border-radius: 0.5em;
  margin: 1em 0;
  overflow-x: auto;
  font-family: ui-monospace, SFMono-Regular, "SF Mono", Consolas,
    "Liberation Mono", Menlo, monospace;
}

.tiptap-editor .ProseMirror pre code {
  background: transparent;
  padding: 0;
  color: inherit;
}

.tiptap-editor .ProseMirror a {
  color: hsl(var(--primary));
  text-decoration: underline;
}

.tiptap-editor .ProseMirror a:hover {
  opacity: 0.8;
}

.tiptap-editor .ProseMirror hr {
  border: none;
  border-top: 2px solid hsl(var(--border));
  margin: 2em 0;
}

.tiptap-editor .ProseMirror img {
  max-width: 100%;
  height: auto;
  border-radius: 0.5em;
  margin: 1em 0;
}

.tiptap-editor .ProseMirror sub {
  vertical-align: sub;
  font-size: smaller;
}

.tiptap-editor .ProseMirror sup {
  vertical-align: super;
  font-size: smaller;
}

/* Text alignment */
.tiptap-editor .ProseMirror [data-text-align="left"] {
  text-align: left;
}

.tiptap-editor .ProseMirror [data-text-align="center"] {
  text-align: center;
}

.tiptap-editor .ProseMirror [data-text-align="right"] {
  text-align: right;
}

.tiptap-editor .ProseMirror [data-text-align="justify"] {
  text-align: justify;
}

/* Task list styles */
.tiptap-editor .ProseMirror ul[data-type="taskList"] {
  list-style: none;
  padding: 0;
}

.tiptap-editor .ProseMirror ul[data-type="taskList"] li {
  display: flex;
  align-items: flex-start;
}

.tiptap-editor .ProseMirror ul[data-type="taskList"] li > label {
  flex: 0 0 auto;
  margin-right: 0.5rem;
  user-select: none;
}

.tiptap-editor .ProseMirror ul[data-type="taskList"] li > div {
  flex: 1 1 auto;
}

/* Placeholder styling */
.tiptap-editor .ProseMirror p.is-editor-empty:first-child::before {
  content: attr(data-placeholder);
  float: left;
  color: hsl(var(--muted-foreground));
  pointer-events: none;
  height: 0;
}

/* Focus styles */
.tiptap-editor .ProseMirror:focus {
  outline: none;
}

/* Selection styles */
.tiptap-editor .ProseMirror ::selection {
  background: hsl(var(--primary) / 0.2);
}

/* Table styles */
.tiptap-editor .ProseMirror table {
  border-collapse: collapse;
  margin: 1em 0;
  width: 100%;
}

.tiptap-editor .ProseMirror table td,
.tiptap-editor .ProseMirror table th {
  border: 1px solid hsl(var(--border));
  padding: 0.5em;
}

.tiptap-editor .ProseMirror table th {
  background: hsl(var(--muted));
  font-weight: 600;
}

/* Prose styling override */
.tiptap-editor.prose {
  max-width: none;
}

.tiptap-editor.prose p {
  margin-top: 0;
  margin-bottom: 1em;
}

.tiptap-editor.prose h1,
.tiptap-editor.prose h2,
.tiptap-editor.prose h3 {
  margin-top: 1.5em;
  margin-bottom: 0.5em;
}

.tiptap-editor.prose h1:first-child,
.tiptap-editor.prose h2:first-child,
.tiptap-editor.prose h3:first-child,
.tiptap-editor.prose p:first-child {
  margin-top: 0;
}
